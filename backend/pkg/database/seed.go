package database

import (
	"log"
	"time"

	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SeedData initializes the database with seed data
func SeedData(db *gorm.DB) {
	// Check if seeding is needed
	var userCount int64
	db.Model(&entities.User{}).Count(&userCount)

	if userCount > 0 {
		log.Println("Database already has data, skipping seed")
		return
	}

	log.Println("Seeding database with initial data...")

	// Create demo user
	demoUser := seedDemoUser(db)

	// Create categories
	expenseCategories := seedExpenseCategories(db, demoUser.ID)
	incomeCategories := seedIncomeCategories(db, demoUser.ID)

	// Create accounts
	accounts := seedAccounts(db, demoUser.ID)

	// Create transactions
	seedTransactions(db, demoUser.ID, expenseCategories, incomeCategories, accounts)

	// Create recurring transactions
	seedRecurringTransactions(db, demoUser.ID, expenseCategories, incomeCategories, accounts)

	log.Println("Database seeding completed successfully")
}

// seedDemoUser creates a demo user
func seedDemoUser(db *gorm.DB) *entities.User {
	hashedPassword := utils.Bcrypt("demo123")

	user := &entities.User{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Username:  "demo",
		Email:     "<EMAIL>",
		Password:  hashedPassword,
		Name:      "Demo User",
		LastLogin: time.Now().Format(time.RFC3339),
		Status:    "active",
	}

	result := db.Create(user)
	if result.Error != nil {
		log.Fatalf("Failed to seed demo user: %v", result.Error)
	}

	log.Println("Demo user created successfully")
	return user
}

// seedExpenseCategories creates default expense categories
func seedExpenseCategories(db *gorm.DB, userID uuid.UUID) []entities.Category {
	expenseCategories := []entities.Category{
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Groceries",
			Type:   "expense",
			Icon:   "shopping-cart",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Dining Out",
			Type:   "expense",
			Icon:   "restaurant",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Transportation",
			Type:   "expense",
			Icon:   "directions-car",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Housing",
			Type:   "expense",
			Icon:   "home",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Utilities",
			Type:   "expense",
			Icon:   "electric-bolt",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Entertainment",
			Type:   "expense",
			Icon:   "movie",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Health",
			Type:   "expense",
			Icon:   "medical-services",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Shopping",
			Type:   "expense",
			Icon:   "shopping-bag",
			UserID: userID,
		},
	}

	for i := range expenseCategories {
		result := db.Create(&expenseCategories[i])
		if result.Error != nil {
			log.Fatalf("Failed to seed expense category %s: %v", expenseCategories[i].Name, result.Error)
		}
	}

	log.Println("Expense categories created successfully")
	return expenseCategories
}

// seedIncomeCategories creates default income categories
func seedIncomeCategories(db *gorm.DB, userID uuid.UUID) []entities.Category {
	incomeCategories := []entities.Category{
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Salary",
			Type:   "income",
			Icon:   "work",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Freelance",
			Type:   "income",
			Icon:   "computer",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Investments",
			Type:   "income",
			Icon:   "trending-up",
			UserID: userID,
		},
		{
			Base:   entities.Base{ID: uuid.New()},
			Name:   "Gifts",
			Type:   "income",
			Icon:   "card-giftcard",
			UserID: userID,
		},
	}

	for i := range incomeCategories {
		result := db.Create(&incomeCategories[i])
		if result.Error != nil {
			log.Fatalf("Failed to seed income category %s: %v", incomeCategories[i].Name, result.Error)
		}
	}

	log.Println("Income categories created successfully")
	return incomeCategories
}

// seedAccounts creates default accounts
func seedAccounts(db *gorm.DB, userID uuid.UUID) []entities.Account {
	accounts := []entities.Account{
		{
			Base:     entities.Base{ID: uuid.New()},
			Name:     "Cash",
			Type:     "cash",
			Balance:  1000,
			Currency: "TRY",
			UserID:   userID,
		},
		{
			Base:     entities.Base{ID: uuid.New()},
			Name:     "Bank Account",
			Type:     "bank",
			Balance:  5000,
			Currency: "TRY",
			UserID:   userID,
		},
		{
			Base:     entities.Base{ID: uuid.New()},
			Name:     "Credit Card",
			Type:     "credit",
			Balance:  0,
			Currency: "TRY",
			UserID:   userID,
		},
	}

	for i := range accounts {
		result := db.Create(&accounts[i])
		if result.Error != nil {
			log.Fatalf("Failed to seed account %s: %v", accounts[i].Name, result.Error)
		}
	}

	log.Println("Accounts created successfully")
	return accounts
}

// seedTransactions creates sample transactions
func seedTransactions(db *gorm.DB, userID uuid.UUID, expenseCategories, incomeCategories []entities.Category, accounts []entities.Account) {
	// Current date for reference
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()

	// Create some transactions for the past 3 months
	for month := 0; month < 3; month++ {
		// Calculate the month
		transactionMonth := time.Date(currentYear, currentMonth-time.Month(month), 1, 0, 0, 0, 0, currentLocation)

		// Income transactions
		// Monthly salary
		createTransaction(db, &entities.Transaction{
			Base:            entities.Base{ID: uuid.New()},
			Title:           "Monthly Salary",
			Type:            "income",
			Amount:          8500,
			Currency:        "TRY",
			CategoryID:      incomeCategories[0].ID, // Salary
			PaymentMethod:   "Bank Transfer",
			AccountID:       accounts[1].ID, // Bank Account
			Note:            "Monthly salary payment",
			TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 15, 10, 0, 0, 0, currentLocation),
			Location:        "Istanbul",
			UserID:          userID,
		})

		// Freelance income (random months)
		if month%2 == 0 {
			createTransaction(db, &entities.Transaction{
				Base:            entities.Base{ID: uuid.New()},
				Title:           "Freelance Project",
				Type:            "income",
				Amount:          2500,
				Currency:        "TRY",
				CategoryID:      incomeCategories[1].ID, // Freelance
				PaymentMethod:   "Bank Transfer",
				AccountID:       accounts[1].ID, // Bank Account
				Note:            "Website development project",
				TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 20, 14, 30, 0, 0, currentLocation),
				Location:        "Remote",
				UserID:          userID,
			})
		}

		// Expense transactions
		// Rent
		createTransaction(db, &entities.Transaction{
			Base:            entities.Base{ID: uuid.New()},
			Title:           "Monthly Rent",
			Type:            "expense",
			Amount:          3000,
			Currency:        "TRY",
			CategoryID:      expenseCategories[3].ID, // Housing
			PaymentMethod:   "Bank Transfer",
			AccountID:       accounts[1].ID, // Bank Account
			Note:            "Monthly apartment rent",
			TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 5, 9, 0, 0, 0, currentLocation),
			Location:        "Istanbul",
			UserID:          userID,
		})

		// Utilities
		createTransaction(db, &entities.Transaction{
			Base:            entities.Base{ID: uuid.New()},
			Title:           "Electricity Bill",
			Type:            "expense",
			Amount:          250,
			Currency:        "TRY",
			CategoryID:      expenseCategories[4].ID, // Utilities
			PaymentMethod:   "Credit Card",
			AccountID:       accounts[2].ID, // Credit Card
			Note:            "Monthly electricity bill",
			TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 10, 16, 45, 0, 0, currentLocation),
			Location:        "Istanbul",
			UserID:          userID,
		})

		// Groceries (weekly)
		for week := 0; week < 4; week++ {
			day := 7*week + 3                  // Every Wednesday
			amount := 350.0 + float64(week*50) // Varying amounts

			createTransaction(db, &entities.Transaction{
				Base:            entities.Base{ID: uuid.New()},
				Title:           "Weekly Groceries",
				Type:            "expense",
				Amount:          amount,
				Currency:        "TRY",
				CategoryID:      expenseCategories[0].ID, // Groceries
				PaymentMethod:   "Credit Card",
				AccountID:       accounts[2].ID, // Credit Card
				Note:            "Weekly grocery shopping",
				TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), day, 18, 30, 0, 0, currentLocation),
				Location:        "Migros Supermarket",
				UserID:          userID,
			})
		}

		// Dining out (twice a month)
		for i := 0; i < 2; i++ {
			day := 10 + i*15                 // 10th and 25th
			amount := 200.0 + float64(i*100) // Varying amounts

			createTransaction(db, &entities.Transaction{
				Base:            entities.Base{ID: uuid.New()},
				Title:           "Restaurant Dinner",
				Type:            "expense",
				Amount:          amount,
				Currency:        "TRY",
				CategoryID:      expenseCategories[1].ID, // Dining Out
				PaymentMethod:   "Credit Card",
				AccountID:       accounts[2].ID, // Credit Card
				Note:            "Dinner with friends",
				TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), day, 20, 0, 0, 0, currentLocation),
				Location:        "Local Restaurant",
				UserID:          userID,
			})
		}

		// Transportation (gas)
		createTransaction(db, &entities.Transaction{
			Base:            entities.Base{ID: uuid.New()},
			Title:           "Fuel",
			Type:            "expense",
			Amount:          500,
			Currency:        "TRY",
			CategoryID:      expenseCategories[2].ID, // Transportation
			PaymentMethod:   "Credit Card",
			AccountID:       accounts[2].ID, // Credit Card
			Note:            "Monthly gas refill",
			TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 12, 14, 15, 0, 0, currentLocation),
			Location:        "Shell Gas Station",
			UserID:          userID,
		})

		// Entertainment
		if month%2 == 0 {
			createTransaction(db, &entities.Transaction{
				Base:            entities.Base{ID: uuid.New()},
				Title:           "Movie Night",
				Type:            "expense",
				Amount:          150,
				Currency:        "TRY",
				CategoryID:      expenseCategories[5].ID, // Entertainment
				PaymentMethod:   "Cash",
				AccountID:       accounts[0].ID, // Cash
				Note:            "Cinema tickets and snacks",
				TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 18, 19, 30, 0, 0, currentLocation),
				Location:        "Cinema",
				UserID:          userID,
			})
		}

		// Shopping
		createTransaction(db, &entities.Transaction{
			Base:            entities.Base{ID: uuid.New()},
			Title:           "Clothing Purchase",
			Type:            "expense",
			Amount:          450,
			Currency:        "TRY",
			CategoryID:      expenseCategories[7].ID, // Shopping
			PaymentMethod:   "Credit Card",
			AccountID:       accounts[2].ID, // Credit Card
			Note:            "New clothes",
			TransactionDate: time.Date(transactionMonth.Year(), transactionMonth.Month(), 22, 15, 0, 0, 0, currentLocation),
			Location:        "Shopping Mall",
			UserID:          userID,
		})
	}

	log.Println("Transactions created successfully")
}

// Helper function to create a transaction
func createTransaction(db *gorm.DB, transaction *entities.Transaction) {
	result := db.Create(transaction)
	if result.Error != nil {
		log.Fatalf("Failed to seed transaction %s: %v", transaction.Title, result.Error)
	}
}

// seedRecurringTransactions creates sample recurring transactions
func seedRecurringTransactions(db *gorm.DB, userID uuid.UUID, expenseCategories, incomeCategories []entities.Category, accounts []entities.Account) {
	// Current date for reference
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()

	// Start date (beginning of current month)
	startDate := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation)

	// Monthly rent payment
	createRecurringTransaction(db, &entities.RecurringTransaction{
		Base:          entities.Base{ID: uuid.New()},
		Title:         "Monthly Rent",
		Type:          "expense",
		Amount:        3000,
		Currency:      "TRY",
		Interval:      "monthly",
		StartDate:     startDate,
		EndDate:       startDate.AddDate(1, 0, 0), // 1 year from now
		CategoryID:    expenseCategories[3].ID,    // Housing
		PaymentMethod: "Bank Transfer",
		AccountID:     accounts[1].ID, // Bank Account
		Note:          "Automatic monthly rent payment",
		UserID:        userID,
	})

	// Monthly salary
	createRecurringTransaction(db, &entities.RecurringTransaction{
		Base:          entities.Base{ID: uuid.New()},
		Title:         "Salary",
		Type:          "income",
		Amount:        8500,
		Currency:      "TRY",
		Interval:      "monthly",
		StartDate:     time.Date(currentYear, currentMonth, 15, 0, 0, 0, 0, currentLocation),
		EndDate:       time.Date(currentYear+1, currentMonth, 15, 0, 0, 0, 0, currentLocation), // 1 year from now
		CategoryID:    incomeCategories[0].ID,                                                  // Salary
		PaymentMethod: "Bank Transfer",
		AccountID:     accounts[1].ID, // Bank Account
		Note:          "Monthly salary deposit",
		UserID:        userID,
	})

	// Weekly grocery shopping
	createRecurringTransaction(db, &entities.RecurringTransaction{
		Base:          entities.Base{ID: uuid.New()},
		Title:         "Grocery Shopping",
		Type:          "expense",
		Amount:        350,
		Currency:      "TRY",
		Interval:      "weekly",
		StartDate:     time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation),
		EndDate:       time.Date(currentYear, currentMonth+3, 1, 0, 0, 0, 0, currentLocation), // 3 months from now
		CategoryID:    expenseCategories[0].ID,                                                // Groceries
		PaymentMethod: "Credit Card",
		AccountID:     accounts[2].ID, // Credit Card
		Note:          "Weekly grocery shopping",
		UserID:        userID,
	})

	// Monthly utility bills
	createRecurringTransaction(db, &entities.RecurringTransaction{
		Base:          entities.Base{ID: uuid.New()},
		Title:         "Electricity Bill",
		Type:          "expense",
		Amount:        250,
		Currency:      "TRY",
		Interval:      "monthly",
		StartDate:     time.Date(currentYear, currentMonth, 10, 0, 0, 0, 0, currentLocation),
		EndDate:       time.Date(currentYear+1, currentMonth, 10, 0, 0, 0, 0, currentLocation), // 1 year from now
		CategoryID:    expenseCategories[4].ID,                                                 // Utilities
		PaymentMethod: "Credit Card",
		AccountID:     accounts[2].ID, // Credit Card
		Note:          "Monthly electricity bill payment",
		UserID:        userID,
	})

	// Monthly internet bill
	createRecurringTransaction(db, &entities.RecurringTransaction{
		Base:          entities.Base{ID: uuid.New()},
		Title:         "Internet Bill",
		Type:          "expense",
		Amount:        150,
		Currency:      "TRY",
		Interval:      "monthly",
		StartDate:     time.Date(currentYear, currentMonth, 15, 0, 0, 0, 0, currentLocation),
		EndDate:       time.Date(currentYear+1, currentMonth, 15, 0, 0, 0, 0, currentLocation), // 1 year from now
		CategoryID:    expenseCategories[4].ID,                                                 // Utilities
		PaymentMethod: "Credit Card",
		AccountID:     accounts[2].ID, // Credit Card
		Note:          "Monthly internet bill payment",
		UserID:        userID,
	})

	log.Println("Recurring transactions created successfully")
}

// Helper function to create a recurring transaction
func createRecurringTransaction(db *gorm.DB, recurringTransaction *entities.RecurringTransaction) {
	result := db.Create(recurringTransaction)
	if result.Error != nil {
		log.Fatalf("Failed to seed recurring transaction %s: %v", recurringTransaction.Title, result.Error)
	}
}
