package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/app/api/routes"
	"github.com/nocytech/butce360/docs"
	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/database"
	"github.com/nocytech/butce360/pkg/domains/account"
	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/domains/bank_statement"
	"github.com/nocytech/butce360/pkg/domains/budget"
	"github.com/nocytech/butce360/pkg/domains/category"
	"github.com/nocytech/butce360/pkg/domains/investment"
	"github.com/nocytech/butce360/pkg/domains/recurring"
	"github.com/nocytech/butce360/pkg/domains/report"
	"github.com/nocytech/butce360/pkg/domains/transaction"
	"github.com/nocytech/butce360/pkg/domains/version"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	// Version routes
	version_repo := version.NewRepo(db)
	version_service := version.NewService(version_repo)
	routes.VersionRoutes(api, version_service)

	// Category routes
	category_repo := category.NewRepo(db)
	category_service := category.NewService(category_repo)
	routes.CategoryRoutes(api)

	// Account routes
	account_repo := account.NewRepo(db)
	account_service := account.NewService(account_repo)
	routes.AccountRoutes(api, account_service)

	// Auth routes (created first for transaction dependency)
	auth_repo := auth.NewRepo(db)
	auth_service := auth.NewService(auth_repo, category_service, account_service)
	routes.AuthRoutes(api, auth_service)

	// Transaction routes (after auth service is created)
	transaction_repo := transaction.NewRepo(db)
	transaction_service := transaction.NewService(transaction_repo, category_service, account_service, auth_service)
	routes.TransactionRoutes(api, transaction_service)

	// Initialize token blacklist repository
	state.SetTokenBlacklistRepository(auth_repo)

	// Bank Statement routes
	bank_statement_repo := bank_statement.NewRepo(db)
	bank_statement_service := bank_statement.NewService(bank_statement_repo, transaction_service, category_service, account_service)
	routes.BankStatementRoutes(api, bank_statement_service)

	// Report routes
	report_repo := report.NewRepo(db)
	report_service := report.NewService(report_repo)
	routes.ReportRoutes(api, report_service)

	// Recurring transaction routes
	recurring_repo := recurring.NewRepo(db)
	recurring_service := recurring.NewService(recurring_repo, category_service, account_service, transaction_service)
	routes.RecurringRoutes(api, recurring_service)

	// Budget routes
	budget_repo := budget.NewRepo(db)
	budget_service := budget.NewService(budget_repo)
	routes.BudgetRoutes(api, budget_service)

	// Investment routes
	investment_repo := investment.NewRepo(db)
	investment_service := investment.NewService(investment_repo)
	routes.InvestmentRoutes(api, investment_service)

	// Routes End <-----

	// Swagger documentation
	docs.SwaggerInfo.Host = config.ReadValue().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")

	// Swagger UI without authentication
	app.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Redirect /docs to Swagger UI
	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})

	// Legacy Swagger UI with authentication
	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "fin_notebook-dev"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "fin_notebook-dev"
	}

	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	// Serve static files from dist directory (both dev and production)
	// Check if dist directory exists
	if _, err := os.Stat("./dist/index.html"); err == nil {
		// Serve static files from the file system in development mode
		app.Static("/static", "./dist/static")
		app.StaticFile("/favicon.ico", "./dist/favicon.ico")
		app.StaticFile("/robots.txt", "./dist/robots.txt")
		app.StaticFile("/manifest.json", "./dist/manifest.json")
		app.StaticFile("/logo192.png", "./dist/logo192.png")
		app.StaticFile("/logo512.png", "./dist/logo512.png")

		// Serve the main index.html for root and all SPA routes
		app.GET("/", func(c *gin.Context) {
			c.File("./dist/index.html")
		})

		// Fallback for unknown routes (SPA routing)
		app.NoRoute(func(c *gin.Context) {
			// Don't serve .txt files or API routes
			if path.Ext(c.Request.URL.Path) == ".txt" {
				c.Status(404)
				return
			}
			// Don't serve index.html for API routes
			if len(c.Request.URL.Path) > 4 && c.Request.URL.Path[:5] == "/api/" {
				c.Status(404)
				return
			}
			// Serve index.html for all other routes (SPA routing)
			c.File("./dist/index.html")
		})
	}

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
