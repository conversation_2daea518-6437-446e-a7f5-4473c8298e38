package middleware

import (
	"net/http"
	"strings"

	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/localizer"
	"github.com/nocytech/butce360/pkg/state"
	"github.com/nocytech/butce360/pkg/utils"
	"github.com/gin-gonic/gin"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func FromClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("client_id")
		if client_id == config.ReadValue().App.ClientID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.<PERSON>(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.ReadValue().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]

		// Check if token is blacklisted
		if state.IsTokenBlacklisted(token) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}

		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseJwtClaim(token)
			c.Set(state.CurrentUserID, claims.UserId)
			c.Set(state.CurrentUserIP, c.ClientIP())
			// Store the token in the context for potential logout
			c.Set(state.CurrentToken, token)

			// Set guest user information if available
			if claims.IsGuest {
				c.Set("is_guest", true)
				c.Set("guest_id", claims.GuestID)
				c.Set("plan", claims.Plan)
			} else {
				c.Set("is_guest", false)
				c.Set("plan", claims.Plan)
			}

			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}
