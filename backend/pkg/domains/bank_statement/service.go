package bank_statement

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/ledongthuc/pdf"
	"github.com/nocytech/butce360/pkg/domains/account"
	"github.com/nocytech/butce360/pkg/domains/category"
	"github.com/nocytech/butce360/pkg/domains/transaction"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/shopspring/decimal"
)

// Service defines the interface for bank statement operations
type Service interface {
	ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ParseEnparaStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ParseGarantiStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ParseCreditCardStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error)
}

// service implements the Service interface
type service struct {
	repository         Repository
	transactionService transaction.Service
	categoryService    category.Service
	accountService     account.Service
}

// NewService creates a new bank statement service
func NewService(r Repository, ts transaction.Service, cs category.Service, as account.Service) Service {
	return &service{
		repository:         r,
		transactionService: ts,
		categoryService:    cs,
		accountService:     as,
	}
}

// getCategoryIDForTransaction finds the best matching category ID from the user's categories
func (s *service) getCategoryIDForTransaction(userID, description, transactionType string) string {
	// Get all categories for the user
	categories, err := s.categoryService.GetAllCategories(userID, transactionType)
	if err != nil {
		fmt.Printf("Error getting categories: %v\n", err)
		// Return empty string if we can't get user categories
		return ""
	}

	// Convert description to lowercase for case-insensitive matching
	lowerDesc := strings.ToLower(description)

	// Switch-based category selection for specific keywords
	categoryID := s.switchBasedCategorySelection(lowerDesc, transactionType, categories)
	if categoryID != "" {
		fmt.Println("to desc based:", lowerDesc)
		fmt.Println("Switch match:", categoryID)
		return categoryID
	}

	fmt.Println("to desc:", lowerDesc)
	// Define comprehensive keywords for different category types
	categoryKeywords := map[string][]string{
		// Income keywords
		"salary":     {"maaş", "salary", "ücret", "wage", "bordro"},
		"transfer":   {"gelen eft", "gelen havale", "gelen fast", "transfer", "virman", "ödeme", "enpara.com cep şubesi", "para transferi"},
		"interest":   {"faiz", "interest", "getiri"},
		"dividend":   {"temettü", "dividend"},
		"rent":       {"kira geliri", "rent income"},
		"freelance":  {"freelance", "serbest", "danışmanlık", "consulting"},
		"investment": {"yatırım", "investment", "hisse", "stock"},

		// Expense keywords - Çok daha kapsamlı
		"groceries":      {"market", "süpermarket", "grocery", "migros", "carrefour", "bim", "a101", "gida", "gıda", "ekmek", "şok", "file", "metro", "real", "makro", "ülker", "eti", "nestle"},
		"dining":         {"restoran", "restaurant", "cafe", "kafe", "yemek", "food", "mcdonalds", "burger", "toros dondurma", "tantuni", "popeyes", "sbux", "starbucks", "firini", "kafterya", "simit", "büfe", "pizza", "döner", "kebap", "lokanta", "aşçı", "chef", "mutfak"},
		"transportation": {"akaryakıt", "benzin", "fuel", "gas", "taksi", "taxi", "ulaşım", "transport", "metro", "bus", "petrol", "yilmaz", "doğancan", "ankale", "shell", "bp", "total", "opet", "po", "aytemiz", "uber", "bitaksi"},
		"utilities":      {"elektrik", "electricity", "su", "water", "doğalgaz", "gas bill", "telefon", "phone", "internet", "fatura", "bill", "turkcell", "vodafone", "türk telekom", "superonline"},
		"housing":        {"kira", "rent", "aidat", "mortgage", "ev", "house", "natopark", "emlak", "konut", "daire"},
		"healthcare":     {"sağlık", "health", "hastane", "hospital", "doktor", "doctor", "eczane", "pharmacy", "ilaç", "medicine", "tedavi", "muayene"},
		"education":      {"eğitim", "education", "okul", "school", "üniversite", "university", "kurs", "course", "kitap", "book", "öğrenim"},
		"shopping":       {"alışveriş", "shopping", "mağaza", "store", "online", "amazon", "trendyol", "apple.com", "apple", "optimum", "ankara", "mall", "avm", "teknosa", "vatan", "media markt", "n11", "hepsiburada", "gittigidiyor"},
		"entertainment":  {"eğlence", "entertainment", "sinema", "cinema", "konser", "concert", "oyun", "game", "atiş poligonu", "cadinin evi", "netflix", "spotify", "youtube", "steam", "playstation", "xbox"},
		"insurance":      {"sigorta", "insurance", "axa", "allianz", "zurich"},
		"tax":            {"vergi", "tax", "gelir vergisi", "kdv", "stopaj"},
		"fee":            {"masraf", "fee", "komisyon", "commission", "nakit avans", "faiz", "ücret", "kkdf", "bsmv", "işlem ücreti"},
		"withdrawal":     {"atm", "para çek", "withdrawal", "nakit", "nakit avans", "cash"},
		"mining":         {"madencilik", "mining", "bitcoin", "crypto"},
		"payment":        {"paycell", "payment", "ödeme", "papara", "ininal"},
		"credit_card":    {"ekstre borcu", "kredi kartı", "credit card", "cc payment", "kart borcu", "ekstre ödemesi", "kredi kartı ödemesi"},
		"clothing":       {"giyim", "clothing", "kıyafet", "ayakkabı", "shoe", "moda", "fashion", "zara", "h&m", "lcw", "koton", "defacto"},
		"beauty":         {"güzellik", "beauty", "kuaför", "berber", "kozmetik", "parfüm", "makyaj"},
		"sports":         {"spor", "sports", "gym", "fitness", "antrenman", "workout", "decathlon", "intersport"},
		"travel":         {"seyahat", "travel", "otel", "hotel", "uçak", "flight", "tatil", "vacation", "booking", "pegasus", "thy"},
	}

	// First, try to find exact or partial matches with category names
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)

		// Check if category name appears in description
		if strings.Contains(lowerDesc, categoryNameLower) || strings.Contains(categoryNameLower, lowerDesc) {
			fmt.Println("Exact match:", category.Name, "-", category.ID, "lower desc:", lowerDesc)
			return category.ID
		}
	}

	// Then, try keyword matching with scoring - More aggressive matching
	bestMatch := ""
	bestScore := 0

	for categoryType, keywords := range categoryKeywords {
		for _, keyword := range keywords {
			if strings.Contains(lowerDesc, keyword) {
				// Calculate score based on keyword length and position
				score := len(keyword)
				if strings.HasPrefix(lowerDesc, keyword) {
					score += 10 // Bonus for starting with keyword
				}

				// Find a category that matches this type
				for _, category := range categories {
					categoryNameLower := strings.ToLower(category.Name)

					// Multiple matching strategies
					matched := false

					// 1. Direct category name match
					if strings.Contains(categoryNameLower, categoryType) ||
						strings.Contains(categoryType, categoryNameLower) ||
						strings.Contains(categoryNameLower, keyword) {
						matched = true
					}

					// 2. Fuzzy matching - check if category name contains any part of the keyword
					if len(keyword) > 3 {
						for i := 0; i <= len(keyword)-3; i++ {
							substr := keyword[i : i+3]
							if strings.Contains(categoryNameLower, substr) {
								matched = true
								score += 2 // Lower score for fuzzy match
								break
							}
						}
					}

					// 3. Check if any word in category name appears in description
					categoryWords := strings.Fields(categoryNameLower)
					for _, word := range categoryWords {
						if len(word) > 2 && strings.Contains(lowerDesc, word) {
							matched = true
							score += 3
							break
						}
					}

					// 4. Semantic matching - related words
					semanticMatches := map[string][]string{
						"food":          {"yemek", "gıda", "market", "restoran", "cafe", "restaurant"},
						"transport":     {"ulaşım", "akaryakıt", "benzin", "taksi", "metro"},
						"entertainment": {"eğlence", "oyun", "sinema", "netflix", "spotify"},
						"health":        {"sağlık", "doktor", "hastane", "eczane", "ilaç"},
						"education":     {"eğitim", "okul", "üniversite", "kitap", "kurs"},
						"utility":       {"fatura", "bill", "elektrik", "su", "telefon"},
						"housing":       {"ev", "kira", "emlak", "konut", "daire"},
						"shopping":      {"alışveriş", "mağaza", "store", "online", "avm"},
						"clothing":      {"giyim", "kıyafet", "ayakkabı", "moda"},
						"beauty":        {"güzellik", "kuaför", "kozmetik", "parfüm"},
						"sports":        {"spor", "gym", "fitness", "antrenman"},
						"travel":        {"seyahat", "otel", "uçak", "tatil"},
					}

					for semantic, words := range semanticMatches {
						if strings.Contains(categoryNameLower, semantic) {
							for _, word := range words {
								if strings.Contains(lowerDesc, word) {
									matched = true
									score += 4
									break
								}
							}
						}
					}

					if matched && score > bestScore {
						bestMatch = category.ID
						bestScore = score
					}
				}
			}
		}
	}

	// If still no match, try more aggressive fallback matching
	if bestMatch == "" {
		for _, category := range categories {
			categoryNameLower := strings.ToLower(category.Name)

			// Split both description and category name into words
			descWords := strings.Fields(lowerDesc)
			catWords := strings.Fields(categoryNameLower)

			// Check for any word overlap
			for _, descWord := range descWords {
				if len(descWord) > 3 { // Only consider words longer than 3 chars
					for _, catWord := range catWords {
						if len(catWord) > 3 && strings.Contains(descWord, catWord) {
							if bestScore < 1 { // Only use if no better match found
								bestMatch = category.ID
								bestScore = 1
							}
						}
					}
				}
			}
		}
	}

	if bestMatch != "" {
		return bestMatch
	}

	// Eğer eşleşme bulunamazsa boş string döndür (kullanıcı frontend'de seçsin)
	return ""
}

// switchBasedCategorySelection performs switch-based category selection for specific keywords
func (s *service) switchBasedCategorySelection(lowerDesc, transactionType string, categories []dtos.CategoryResponse) string {
	// Önce Garanti'ye özel switch kontrollerini yap
	garantiResult := s.garantiSwitchBasedCategorySelection(lowerDesc, transactionType, categories)
	if garantiResult != "" {
		return garantiResult
	}

	// Sonra genel switch kontrollerini yap
	switch {
	// Market ve Gıda
	case strings.Contains(lowerDesc, "migros"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})
	case strings.Contains(lowerDesc, "carrefour"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})
	case strings.Contains(lowerDesc, "bim"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})
	case strings.Contains(lowerDesc, "a101"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})
	case strings.Contains(lowerDesc, "şok"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})

	// Yemek ve Restoran
	case strings.Contains(lowerDesc, "starbucks"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food", "cafe"})
	case strings.Contains(lowerDesc, "dondurma"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food", "cafe"})
	case strings.Contains(lowerDesc, "mcdonalds"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "burger"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "pizza"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "döner"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "kebap"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "lokanta"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food"})
	case strings.Contains(lowerDesc, "cafe"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food", "cafe"})
	case strings.Contains(lowerDesc, "kafe"):
		return s.findCategoryByName(categories, []string{"yemek", "restoran", "dining", "food", "cafe"})

	// Akaryakıt ve Ulaşım
	case strings.Contains(lowerDesc, "shell"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "bp"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "total"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "opet"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "aytemiz"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "benzin"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "akaryakıt"):
		return s.findCategoryByName(categories, []string{"akaryakıt", "benzin", "ulaşım", "transport", "fuel"})
	case strings.Contains(lowerDesc, "taksi"):
		return s.findCategoryByName(categories, []string{"ulaşım", "transport", "taksi"})
	case strings.Contains(lowerDesc, "uber"):
		return s.findCategoryByName(categories, []string{"ulaşım", "transport", "taksi"})
	case strings.Contains(lowerDesc, "bitaksi"):
		return s.findCategoryByName(categories, []string{"ulaşım", "transport", "taksi"})

	// Alışveriş ve Online
	case strings.Contains(lowerDesc, "amazon"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online"})
	case strings.Contains(lowerDesc, "trendyol"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online"})
	case strings.Contains(lowerDesc, "hepsiburada"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online"})
	case strings.Contains(lowerDesc, "n11"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online"})
	case strings.Contains(lowerDesc, "gittigidiyor"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online"})
	case strings.Contains(lowerDesc, "apple.com"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "online", "teknoloji"})
	case strings.Contains(lowerDesc, "teknosa"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "teknoloji"})
	case strings.Contains(lowerDesc, "vatan"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "teknoloji"})
	case strings.Contains(lowerDesc, "media markt"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "teknoloji"})

	// Eğlence ve Dijital Hizmetler
	case strings.Contains(lowerDesc, "netflix"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "dijital"})
	case strings.Contains(lowerDesc, "spotify"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "dijital"})
	case strings.Contains(lowerDesc, "youtube"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "dijital"})
	case strings.Contains(lowerDesc, "steam"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "oyun", "game"})
	case strings.Contains(lowerDesc, "playstation"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "oyun", "game"})
	case strings.Contains(lowerDesc, "xbox"):
		return s.findCategoryByName(categories, []string{"eğlence", "entertainment", "oyun", "game"})

	// Giyim ve Moda
	case strings.Contains(lowerDesc, "zara"):
		return s.findCategoryByName(categories, []string{"giyim", "clothing", "moda", "fashion"})
	case strings.Contains(lowerDesc, "h&m"):
		return s.findCategoryByName(categories, []string{"giyim", "clothing", "moda", "fashion"})
	case strings.Contains(lowerDesc, "lcw"):
		return s.findCategoryByName(categories, []string{"giyim", "clothing", "moda", "fashion"})
	case strings.Contains(lowerDesc, "koton"):
		return s.findCategoryByName(categories, []string{"giyim", "clothing", "moda", "fashion"})
	case strings.Contains(lowerDesc, "defacto"):
		return s.findCategoryByName(categories, []string{"giyim", "clothing", "moda", "fashion"})

	// Faturalar ve Hizmetler
	case strings.Contains(lowerDesc, "turkcell"):
		return s.findCategoryByName(categories, []string{"fatura", "telefon", "utilities", "bill"})
	case strings.Contains(lowerDesc, "vodafone"):
		return s.findCategoryByName(categories, []string{"fatura", "telefon", "utilities", "bill"})
	case strings.Contains(lowerDesc, "türk telekom"):
		return s.findCategoryByName(categories, []string{"fatura", "telefon", "internet", "utilities", "bill"})
	case strings.Contains(lowerDesc, "superonline"):
		return s.findCategoryByName(categories, []string{"fatura", "internet", "utilities", "bill"})
	case strings.Contains(lowerDesc, "elektrik"):
		return s.findCategoryByName(categories, []string{"fatura", "elektrik", "utilities", "bill"})
	case strings.Contains(lowerDesc, "doğalgaz"):
		return s.findCategoryByName(categories, []string{"fatura", "doğalgaz", "utilities", "bill"})
	case strings.Contains(lowerDesc, "su faturası"):
		return s.findCategoryByName(categories, []string{"fatura", "su", "utilities", "bill"})

	// Kredi Kartı ve Bankacılık
	case strings.Contains(lowerDesc, "ekstre borcu"):
		return s.findCategoryByName(categories, []string{"kredi kartı", "credit card", "banka", "banking"})
	case strings.Contains(lowerDesc, "kredi kartı"):
		return s.findCategoryByName(categories, []string{"kredi kartı", "credit card", "banka", "banking"})
	case strings.Contains(lowerDesc, "kart borcu"):
		return s.findCategoryByName(categories, []string{"kredi kartı", "credit card", "banka", "banking"})
	case strings.Contains(lowerDesc, "atm"):
		return s.findCategoryByName(categories, []string{"nakit", "atm", "withdrawal", "banka"})
	case strings.Contains(lowerDesc, "nakit"):
		return s.findCategoryByName(categories, []string{"nakit", "atm", "withdrawal", "banka"})

	// Sağlık
	case strings.Contains(lowerDesc, "eczane"):
		return s.findCategoryByName(categories, []string{"sağlık", "health", "eczane", "ilaç"})
	case strings.Contains(lowerDesc, "hastane"):
		return s.findCategoryByName(categories, []string{"sağlık", "health", "hastane", "doktor"})
	case strings.Contains(lowerDesc, "doktor"):
		return s.findCategoryByName(categories, []string{"sağlık", "health", "doktor"})

	// Gelir türleri
	case strings.Contains(lowerDesc, "maaş") && transactionType == "income":
		return s.findCategoryByName(categories, []string{"maaş", "salary", "gelir", "income"})
	case strings.Contains(lowerDesc, "gelen eft") && transactionType == "income":
		return s.findCategoryByName(categories, []string{"transfer", "eft", "gelir", "income"})
	case strings.Contains(lowerDesc, "gelen havale") && transactionType == "income":
		return s.findCategoryByName(categories, []string{"transfer", "havale", "gelir", "income"})

	default:
		return ""
	}
}

// garantiSwitchBasedCategorySelection - Garanti için özel kategoriler
func (s *service) garantiSwitchBasedCategorySelection(lowerDesc, transactionType string, categories []dtos.CategoryResponse) string {
	// Garanti'ye özel switch cases
	switch {
	// Garanti ATM işlemleri
	case strings.Contains(lowerDesc, "garanti atm"):
		return s.findCategoryByName(categories, []string{"nakit", "atm", "withdrawal", "banka"})
	case strings.Contains(lowerDesc, "garantibank atm"):
		return s.findCategoryByName(categories, []string{"nakit", "atm", "withdrawal", "banka"})

	// Garanti özel işlemler
	case strings.Contains(lowerDesc, "garanti pos"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "pos"})
	case strings.Contains(lowerDesc, "garanti internet"):
		return s.findCategoryByName(categories, []string{"fatura", "internet", "utilities", "bill"})
	case strings.Contains(lowerDesc, "garanti mobil"):
		return s.findCategoryByName(categories, []string{"fatura", "telefon", "utilities", "bill"})

	// Garanti bonus işlemleri
	case strings.Contains(lowerDesc, "bonus"):
		return s.findCategoryByName(categories, []string{"alışveriş", "shopping", "bonus"})
	case strings.Contains(lowerDesc, "bonus market"):
		return s.findCategoryByName(categories, []string{"market", "gıda", "groceries", "food"})

	// Garanti kredi kartı
	case strings.Contains(lowerDesc, "garanti kart"):
		return s.findCategoryByName(categories, []string{"kredi kartı", "credit card", "banka", "banking"})
	case strings.Contains(lowerDesc, "garanti cc"):
		return s.findCategoryByName(categories, []string{"kredi kartı", "credit card", "banka", "banking"})

	default:
		return ""
	}
}

// getAccountIDForTransaction finds the best matching account ID from the user's accounts
func (s *service) getAccountIDForTransaction(userID, _ string) string {
	// Get all accounts for the user
	accounts, err := s.accountService.GetAllAccounts(userID)
	if err != nil {
		fmt.Printf("Error getting accounts: %v\n", err)
		// Return empty string if we can't get user accounts
		return ""
	}

	// If user has accounts, return the first one (default account)
	if len(accounts) > 0 {
		return accounts[0].ID
	}

	// No account found, return empty string
	return ""
}

// ParseVakifbankStatement parses a Vakifbank statement PDF and extracts transactions
func (s *service) ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, fmt.Errorf("pdf dosyası okunamadı: %w", err)
	}

	// PDF dökümanını aç
	pdfReader, err := pdf.NewReader(bytes.NewReader(buf.Bytes()), int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("pdf okunamadı: %w", err)
	}

	// Tüm sayfalardaki metni birleştir
	var allText string
	numPages := pdfReader.NumPage()
	for i := 1; i <= numPages; i++ {
		page := pdfReader.Page(i)
		if page.V.IsNull() {
			continue
		}

		// Sayfadaki tüm içerikleri dolaş
		content := page.Content()
		if content.Text == nil || len(content.Text) == 0 {
			continue
		}

		for _, text := range content.Text {
			allText += text.S
		}
	}

	// PDF içeriğini alıp işleme fonksiyonuna gönder
	return s.parseVakifbankStatementText(userID, allText)
}

// parseVakifbankStatementText parses the extracted text from a Vakifbank statement
func (s *service) parseVakifbankStatementText(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	scanner := bufio.NewScanner(strings.NewReader(text))
	var lines []string
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, line)
		}
	}

	dateRegex := regexp.MustCompile(`^\d{2}\.\d{2}\.\d{4}$`)
	// timeRegex := regexp.MustCompile(`^\d{2}:\d{2}$`)
	// amountRegex := regexp.MustCompile(`^-?[\d.]+,\d{2}$`)

	i := 0
	for i < len(lines) {
		line := lines[i]
		if dateRegex.MatchString(line) && i+4 < len(lines) {
			date := lines[i]
			timeVal := lines[i+1]
			amountStr := lines[i+3]
			descriptionLines := []string{}
			explanation := ""

			// Miktarı parse et
			amountClean := strings.ReplaceAll(amountStr, ".", "")
			amountClean = strings.ReplaceAll(amountClean, ",", ".")
			amountFloat, err := strconv.ParseFloat(amountClean, 64)
			if err != nil {
				i++
				continue
			}

			// Açıklamaları topla
			j := i + 5
			for j < len(lines) {
				if dateRegex.MatchString(lines[j]) {
					break
				}
				descriptionLines = append(descriptionLines, lines[j])
				j++
			}

			// Açıklamanın içinde parantezli detay varsa ayır
			fullDescription := strings.Join(descriptionLines, " ")
			fullDescription = strings.ReplaceAll(fullDescription, "�", " ") // garip karakter temizliği

			if start := strings.Index(fullDescription, "("); start != -1 {
				if end := strings.Index(fullDescription, ")"); end > start {
					explanation = fullDescription[start : end+1]
					fullDescription = strings.TrimSpace(fullDescription[:start])
				}
			}

			transactionType := "expense"
			if amountFloat > 0 {
				transactionType = "income"
			}

			categoryID := s.getCategoryIDForTransaction(userID, fullDescription, transactionType)
			accountID := s.getAccountIDForTransaction(userID, fullDescription)

			// Eşleşmeyen kategoriler boş kalacak, kullanıcı frontend'de seçecek

			entry := dtos.BankStatementEntry{
				Date:        fmt.Sprintf("%s %s", date, timeVal),
				Description: strings.TrimSpace(fullDescription + " " + explanation),
				Amount:      math.Abs(amountFloat),
				Type:        transactionType,
				CategoryID:  categoryID,
				AccountID:   accountID,
			}
			entries = append(entries, entry)
			i = j
		} else {
			i++
		}
	}

	fmt.Printf("Found %d transaction entries\n", len(entries))
	fmt.Println("Entries:", entries)
	return entries, nil
}

// ImportBankStatementEntries imports bank statement entries as transactions
func (s *service) ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error) {
	var responses []dtos.TransactionResponse

	for _, entry := range entries {
		// Convert date format if needed
		var transactionDate time.Time
		var err error

		// Try different date formats
		dateFormats := []string{
			"02.01.2006 15:04",    // DD.MM.YYYY HH:MM (from PDF parsing)
			"02.01.2006",          // DD.MM.YYYY
			"2006-01-02",          // YYYY-MM-DD
			"2006-01-02 15:04:05", // YYYY-MM-DD HH:MM:SS
		}

		for _, format := range dateFormats {
			transactionDate, err = time.Parse(format, entry.Date)
			if err == nil {
				break
			}
		}

		if err != nil {
			return nil, fmt.Errorf("invalid date format: %s", entry.Date)
		}

		// Use provided category/account or assign default if empty
		categoryID := entry.CategoryID
		accountID := entry.AccountID

		// Eğer kategori/hesap boşsa, default ata (import sırasında zorunlu)
		if categoryID == "" {
			if entry.Type == "expense" {
				categoryID = s.getDefaultExpenseCategoryID(userID)
			} else {
				categoryID = s.getDefaultIncomeCategoryID(userID)
			}
		}

		if accountID == "" {
			accountID = s.getDefaultAccountID(userID)
		}

		// Create transaction request
		transactionReq := &dtos.TransactionRequest{
			Title:           entry.Description,
			Type:            entry.Type,
			Amount:          entry.Amount,
			Currency:        "TRY",           // Default to Turkish Lira
			CategoryID:      categoryID,      // Auto-assigned if needed
			PaymentMethod:   "bank_transfer", // Default payment method
			AccountID:       accountID,       // Auto-assigned if needed
			TransactionDate: transactionDate,
		}

		// Create transaction
		response, err := s.transactionService.CreateTransaction(userID, transactionReq)
		if err != nil {
			return nil, err
		}

		responses = append(responses, *response)
	}

	return responses, nil
}

// ParseEnparaStatement parses an Enpara statement PDF and extracts transactions
func (s *service) ParseEnparaStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, fmt.Errorf("pdf dosyası okunamadı: %w", err)
	}

	// PDF dökümanını aç
	pdfReader, err := pdf.NewReader(bytes.NewReader(buf.Bytes()), int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("pdf okunamadı: %w", err)
	}

	// Tüm sayfalardaki metni birleştir
	var allText string
	numPages := pdfReader.NumPage()
	for i := 1; i <= numPages; i++ {
		page := pdfReader.Page(i)
		if page.V.IsNull() {
			continue
		}

		// Sayfadaki metni al
		pageText, err := page.GetPlainText(nil)
		if err != nil {
			fmt.Printf("Error extracting text from page %d: %v\n", i, err)
			continue
		}

		allText += pageText + "\n"
	}

	fmt.Println("all text:", allText)

	// PDF içeriğini alıp işleme fonksiyonuna gönder
	return s.parseEnparaStatementTextV2(userID, allText)
}

// parseEnparaStatementText parses the extracted text from an Enpara statement
func (s *service) parseEnparaStatementText(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	fmt.Println("Enpara text:", text)

	lines := strings.Split(text, "\n")

	// Tablo başlığını bul (Tarih, Açıklama, Tutar, Bakiye)
	tableStartIndex := -1

	for i := 0; i < len(lines)-3; i++ {
		// 4 ardışık satırda Tarih, Açıklama, Tutar, Bakiye'yi ara
		if strings.TrimSpace(strings.ToLower(lines[i])) == "tarih" &&
			strings.TrimSpace(strings.ToLower(lines[i+1])) == "açıklama" &&
			strings.TrimSpace(strings.ToLower(lines[i+2])) == "tutar" &&
			strings.TrimSpace(strings.ToLower(lines[i+3])) == "bakiye" {
			tableStartIndex = i + 4
			fmt.Printf("Found transaction table header starting at line %d\n", i)
			break
		}
	}

	if tableStartIndex == -1 {
		fmt.Println("Transaction table header not found")
		return entries, nil
	}

	fmt.Printf("Processing transactions from line %d onwards\n", tableStartIndex)

	// Tarih regex'i - DD/MM/YY formatı
	dateRegex := regexp.MustCompile(`^(\d{2}/\d{2}/\d{2,4})`)

	for i := tableStartIndex; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// Tarih ile başlayan satırları bul ve gerçek işlem satırı olduğunu doğrula
		if dateRegex.MatchString(line) {

			fmt.Printf("Processing transaction line: %s\n", line)

			// Satırı parse et - genellikle birden fazla satıra yayılmış olabilir
			var fullLine string = line

			// Sonraki satırları da kontrol et (açıklama devam edebilir)
			j := i + 1
			for j < len(lines) && !dateRegex.MatchString(strings.TrimSpace(lines[j])) {
				nextLine := strings.TrimSpace(lines[j])

				if nextLine != "" && !strings.Contains(nextLine, "Sayfa") &&
					!strings.Contains(nextLine, "QNB") && !strings.Contains(nextLine, "Enpara") {
					fullLine += " " + nextLine
				}
				j++
			}

			fmt.Printf("Full line: %s\n", fullLine)

			// Regex ile parse et - genel pattern kullan
			// Pattern: Tarih + İşlem Açıklaması + Miktar TL + Bakiye TL
			// Herhangi bir işlem açıklamasını kabul et, sadece format kontrolü yap
			transactionRegex := regexp.MustCompile(`(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+([-+]?\s*[\d.,]+)\s*TL\s+([-+]?\s*[\d.,]+)\s*TL`)
			match := transactionRegex.FindStringSubmatch(fullLine)

			// Eğer genel pattern match etmezse, alternatif pattern dene
			if len(match) < 5 {
				// Döviz işlemleri için alternatif pattern (TRY karşılığı olan)
				currencyRegex := regexp.MustCompile(`(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+([-+]?\s*[\d.,]+)\s*[A-Z]{3}\s*\(TRY\s*([-+]?\s*[\d.,]+)\)\s+([-+]?\s*[\d.,]+)\s*TL`)
				currencyMatch := currencyRegex.FindStringSubmatch(fullLine)
				if len(currencyMatch) >= 6 {
					// Döviz işlemi - TRY karşılığını kullan
					match = []string{currencyMatch[0], currencyMatch[1], currencyMatch[2], currencyMatch[4], currencyMatch[5]}
					fmt.Printf("Matched as currency transaction: %s\n", fullLine)
				}
			}

			if len(match) >= 5 {
				date := match[1]
				description := strings.TrimSpace(match[2])
				transactionAmountStr := strings.ReplaceAll(strings.TrimSpace(match[3]), " ", "")
				balanceAmountStr := strings.ReplaceAll(strings.TrimSpace(match[4]), " ", "")

				// Ek validasyon - açıklama çok kısa veya sadece sayılardan oluşuyorsa skip et
				if len(description) < 3 {
					fmt.Printf("Skipping line with too short description: %s\n", description)
					continue
				}

				// Açıklama sadece sayı ve boşluklardan oluşuyorsa skip et
				if regexp.MustCompile(`^[\d\s.,]+$`).MatchString(description) {
					fmt.Printf("Skipping line with numeric-only description: %s\n", description)
					continue
				}

				// Açıklamada sayı varsa, işlem tutarı güvenilir değil
				// Bu durumda bakiye sütunundaki değeri kullan (daha güvenilir)
				descriptionHasNumbers := regexp.MustCompile(`\d+[.,]?\d*`).MatchString(description)

				var amountStr string
				if descriptionHasNumbers {
					// Açıklamada sayı varsa, regex'in yanlış parse etme riski yüksek
					// Bakiye değişimini hesaplamak yerine, transaction amount'u kullan ama dikkatli ol
					amountStr = transactionAmountStr
					fmt.Printf("WARNING: Description contains numbers, parsed amount may be incorrect: %s -> %s\n", description, amountStr)

					// Eğer tutar çok büyükse (>100000), muhtemelen yanlış parse edilmiş
					if testAmount, err := decimal.NewFromString(strings.ReplaceAll(strings.ReplaceAll(amountStr, ".", ""), ",", ".")); err == nil {
						if testAmount.GreaterThan(decimal.NewFromInt(100000)) {
							fmt.Printf("ERROR: Parsed amount seems too large (%s), skipping this transaction\n", amountStr)
							continue
						}
					}
				} else {
					amountStr = transactionAmountStr
				}

				// balanceAmountStr'yi log için kullan
				fmt.Printf("Transaction: %s, Balance: %s\n", transactionAmountStr, balanceAmountStr)

				fmt.Printf("Parsed - Date: %s, Description: %s, Amount: %s\n", date, description, amountStr)

				// Miktar işleme
				isNegative := strings.HasPrefix(amountStr, "-")
				amountStr = strings.TrimPrefix(amountStr, "-")
				amountStr = strings.TrimPrefix(amountStr, "+")

				// Nokta ve virgül düzenlemesi
				amountStr = strings.ReplaceAll(amountStr, ".", "")
				amountStr = strings.ReplaceAll(amountStr, ",", ".")

				amount, err := decimal.NewFromString(amountStr)
				if err != nil {
					fmt.Printf("Error parsing amount: %s, error: %v\n", amountStr, err)
					continue
				}

				// İşlem tipini belirle
				transactionType := "income"
				if isNegative {
					transactionType = "expense"
				}

				// Tarih formatını düzenle
				formattedDate := strings.ReplaceAll(date, "/", ".")
				// 2 haneli yılı 4 haneli yap
				if len(strings.Split(formattedDate, ".")[2]) == 2 {
					parts := strings.Split(formattedDate, ".")
					formattedDate = parts[0] + "." + parts[1] + ".20" + parts[2]
				}

				// Kategori ID'sini otomatik ata
				categoryID := s.getCategoryIDForTransaction(userID, description, transactionType)
				accountID := s.getAccountIDForTransaction(userID, description)

				entry := dtos.BankStatementEntry{
					Date:        formattedDate,
					Description: strings.TrimSpace(description),
					Amount:      amount.InexactFloat64(),
					Type:        transactionType,
					CategoryID:  categoryID,
					AccountID:   accountID,
				}
				entries = append(entries, entry)
				fmt.Printf("Added entry: %+v\n", entry)
			} else {
				// Son çare: daha basit pattern dene (sadece tarih + metin + TL)
				simpleRegex := regexp.MustCompile(`(\d{2}/\d{2}/\d{2,4})\s+(.+?)\s+([-+]?\s*[\d.,]+)\s*TL`)
				simpleMatch := simpleRegex.FindStringSubmatch(fullLine)

				if len(simpleMatch) >= 4 {
					// Basit format bulundu, bakiye bilgisi yok ama işlem var
					fmt.Printf("Matched with simple pattern (no balance): %s\n", fullLine)

					date := simpleMatch[1]
					description := strings.TrimSpace(simpleMatch[2])
					transactionAmountStr := strings.ReplaceAll(strings.TrimSpace(simpleMatch[3]), " ", "")

					// Basit validasyon
					if len(description) < 3 || regexp.MustCompile(`^[\d\s.,]+$`).MatchString(description) {
						fmt.Printf("Skipping simple pattern line with invalid description: %s\n", description)
						continue
					}

					// İşlem tutarı işleme
					isNegative := strings.HasPrefix(transactionAmountStr, "-")
					amountStr := strings.TrimPrefix(transactionAmountStr, "-")
					amountStr = strings.TrimPrefix(amountStr, "+")
					amountStr = strings.ReplaceAll(amountStr, ".", "")
					amountStr = strings.ReplaceAll(amountStr, ",", ".")

					amount, err := decimal.NewFromString(amountStr)
					if err != nil {
						fmt.Printf("Error parsing simple pattern amount: %s, error: %v\n", amountStr, err)
						continue
					}

					// Çok büyük tutarları skip et
					if amount.GreaterThan(decimal.NewFromInt(100000)) {
						fmt.Printf("Simple pattern amount too large, skipping: %s\n", amountStr)
						continue
					}

					// İşlem tipini belirle
					transactionType := "income"
					if isNegative {
						transactionType = "expense"
					}

					// Tarih formatını düzenle
					formattedDate := strings.ReplaceAll(date, "/", ".")
					if len(strings.Split(formattedDate, ".")[2]) == 2 {
						parts := strings.Split(formattedDate, ".")
						year, _ := strconv.Atoi(parts[2])
						if year < 50 {
							year += 2000
						} else {
							year += 1900
						}
						formattedDate = fmt.Sprintf("%s.%s.%d", parts[0], parts[1], year)
					}

					// Kategori ve hesap ID'si ata
					categoryID := s.getCategoryIDForTransaction(userID, description, transactionType)
					accountID := s.getAccountIDForTransaction(userID, description)

					entry := dtos.BankStatementEntry{
						Date:        formattedDate,
						Description: description,
						Amount:      amount.InexactFloat64(),
						Type:        transactionType,
						CategoryID:  categoryID,
						AccountID:   accountID,
					}

					entries = append(entries, entry)
					fmt.Printf("Added simple pattern transaction: %+v\n", entry)

					i = j - 1 // Sonraki satırdan devam et
					continue
				}

				fmt.Printf("Line does not match any transaction pattern: %s\n", fullLine)
				continue
			}
			// İşlenen satırları atla
			i = j - 1
		}
	}

	fmt.Printf("Found %d Enpara transaction entries\n", len(entries))
	return entries, nil
}

// parseEnparaStatementTextV2 parses the extracted text from an Enpara statement using improved algorithm
func (s *service) parseEnparaStatementTextV2(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	fmt.Println("Enpara V2 text:", text)

	lines := strings.Split(text, "\n")

	// Tablo başlığını bul (Tarih, Açıklama, Tutar, Bakiye)
	tableStartIndex := -1

	for i := 0; i < len(lines)-3; i++ {
		// 4 ardışık satırda Tarih, Açıklama, Tutar, Bakiye'yi ara
		if strings.TrimSpace(strings.ToLower(lines[i])) == "tarih" &&
			strings.TrimSpace(strings.ToLower(lines[i+1])) == "açıklama" &&
			strings.TrimSpace(strings.ToLower(lines[i+2])) == "tutar" &&
			strings.TrimSpace(strings.ToLower(lines[i+3])) == "bakiye" {
			tableStartIndex = i + 4
			fmt.Printf("V2: Found transaction table header starting at line %d\n", i)
			break
		}
	}

	if tableStartIndex == -1 {
		fmt.Println("V2: Transaction table header not found")
		return entries, nil
	}

	fmt.Printf("V2: Processing transactions from line %d onwards\n", tableStartIndex)

	// Tarih regex'i - DD/MM/YY formatı
	dateRegex := regexp.MustCompile(`^(\d{2}/\d{2}/\d{2,4})`)
	// TL tutarı regex'i - sayı + TL formatı
	tlAmountRegex := regexp.MustCompile(`([-+]?\s*[\d.,]+)\s*TL`)

	for i := tableStartIndex; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// Tarih ile başlayan satırları bul
		if dateRegex.MatchString(line) {
			fmt.Printf("V2: Processing transaction starting at line %d: %s\n", i, line)

			// Tarihi al
			dateMatch := dateRegex.FindStringSubmatch(line)
			if len(dateMatch) < 2 {
				continue
			}
			date := dateMatch[1]

			// Sonraki tarih satırını bul (veya dosya sonu)
			nextDateIndex := len(lines)
			for j := i + 1; j < len(lines); j++ {
				nextLine := strings.TrimSpace(lines[j])
				if dateRegex.MatchString(nextLine) {
					nextDateIndex = j
					break
				}
			}

			fmt.Printf("V2: Transaction block from line %d to %d\n", i, nextDateIndex-1)

			// Bu tarihten sonraki tarih'e kadar olan tüm satırları birleştir
			var blockText string
			for j := i; j < nextDateIndex; j++ {
				blockLine := strings.TrimSpace(lines[j])
				if blockLine != "" && !strings.Contains(blockLine, "Sayfa") {
					// Sadece sayfa numarası satırlarını atla
					// QNB ve Enpara kelimelerini sadece sayfa başlığı olduğunda atla
					// İşlem açıklamalarında bu kelimeler varsa dahil et
					lowerLine := strings.ToLower(blockLine)

					// Sayfa başlığı kontrolü - bu durumda atla
					if (strings.Contains(lowerLine, "qnb") && strings.Contains(lowerLine, "enpara")) ||
						(strings.Contains(lowerLine, "enpara.com") && !strings.Contains(lowerLine, "ödeme")) {
						continue
					}

					blockText += " " + blockLine
				}
			}

			blockText = strings.TrimSpace(blockText)
			fmt.Printf("V2: Block text: %s\n", blockText)

			// Blok içindeki tüm TL tutarlarını bul
			tlMatches := tlAmountRegex.FindAllStringSubmatch(blockText, -1)

			if len(tlMatches) < 2 {
				fmt.Printf("V2: Not enough TL amounts found (%d), skipping\n", len(tlMatches))
				i = nextDateIndex - 1
				continue
			}

			fmt.Printf("V2: Found %d TL amounts\n", len(tlMatches))
			for idx, match := range tlMatches {
				fmt.Printf("V2: TL amount %d: %s\n", idx, match[1])
			}

			// Son iki TL tutarını al
			tutar := strings.TrimSpace(tlMatches[len(tlMatches)-2][1])  // Sondan ikinci = Tutar
			bakiye := strings.TrimSpace(tlMatches[len(tlMatches)-1][1]) // Sonuncu = Bakiye

			fmt.Printf("V2: Tutar: %s, Bakiye: %s\n", tutar, bakiye)

			// Açıklamayı bul: tarihten sonra, tutardan önceki kısım
			// İlk TL tutarının pozisyonunu bul
			firstTlIndex := strings.Index(blockText, tlMatches[len(tlMatches)-2][0])
			if firstTlIndex == -1 {
				fmt.Printf("V2: Could not find tutar position in block\n")
				i = nextDateIndex - 1
				continue
			}

			// Tarih kısmını çıkar
			afterDate := blockText[len(date):]
			// Tutar kısmını çıkar
			tutarIndex := strings.Index(afterDate, tlMatches[len(tlMatches)-2][0])
			if tutarIndex == -1 {
				fmt.Printf("V2: Could not find tutar in after-date text\n")
				i = nextDateIndex - 1
				continue
			}

			description := strings.TrimSpace(afterDate[:tutarIndex])
			fmt.Printf("V2: Description: %s\n", description)

			// Açıklama validasyonu
			if len(description) < 3 {
				fmt.Printf("V2: Description too short, skipping\n")
				i = nextDateIndex - 1
				continue
			}

			// Tutarı parse et
			isNegative := strings.HasPrefix(tutar, "-")
			tutarClean := strings.TrimPrefix(tutar, "-")
			tutarClean = strings.TrimPrefix(tutarClean, "+")
			tutarClean = strings.ReplaceAll(tutarClean, " ", "")

			// Nokta ve virgül düzenlemesi
			tutarClean = strings.ReplaceAll(tutarClean, ".", "")
			tutarClean = strings.ReplaceAll(tutarClean, ",", ".")

			amount, err := decimal.NewFromString(tutarClean)
			if err != nil {
				fmt.Printf("V2: Error parsing amount: %s, error: %v\n", tutarClean, err)
				i = nextDateIndex - 1
				continue
			}

			// İşlem tipini belirle
			transactionType := "income"
			if isNegative {
				transactionType = "expense"
			}

			// Tarih formatını düzenle
			formattedDate := strings.ReplaceAll(date, "/", ".")
			// 2 haneli yılı 4 haneli yap
			if len(strings.Split(formattedDate, ".")[2]) == 2 {
				parts := strings.Split(formattedDate, ".")
				formattedDate = parts[0] + "." + parts[1] + ".20" + parts[2]
			}

			// Kategori ID'sini otomatik ata
			categoryID := s.getCategoryIDForTransaction(userID, description, transactionType)
			accountID := s.getAccountIDForTransaction(userID, description)

			entry := dtos.BankStatementEntry{
				Date:        formattedDate,
				Description: strings.TrimSpace(description),
				Amount:      amount.InexactFloat64(),
				Type:        transactionType,
				CategoryID:  categoryID,
				AccountID:   accountID,
			}
			entries = append(entries, entry)
			fmt.Printf("V2: Added entry: %+v\n", entry)

			// Sonraki tarih satırından devam et
			i = nextDateIndex - 1
		}
	}

	fmt.Printf("V2: Found %d Enpara transaction entries\n", len(entries))
	return entries, nil
}

// ParseCreditCardStatement parses a Credit Card statement PDF and extracts transactions
func (s *service) ParseCreditCardStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, fmt.Errorf("pdf dosyası okunamadı: %w", err)
	}

	// PDF dökümanını aç
	pdfReader, err := pdf.NewReader(bytes.NewReader(buf.Bytes()), int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("pdf okunamadı: %w", err)
	}

	// Tüm sayfalardaki metni birleştir
	var allText string
	numPages := pdfReader.NumPage()
	for i := 1; i <= numPages; i++ {
		page := pdfReader.Page(i)
		if page.V.IsNull() {
			continue
		}

		// Sayfadaki metni al
		pageText, err := page.GetPlainText(nil)
		if err != nil {
			fmt.Printf("Error extracting text from page %d: %v\n", i, err)
			continue
		}

		allText += pageText + "\n"
	}

	fmt.Println("Credit Card text:", allText)

	// PDF içeriğini alıp işleme fonksiyonuna gönder
	return s.parseCreditCardStatementText(userID, allText)
}

// parseCreditCardStatementText parses the extracted text from a Credit Card statement
func (s *service) parseCreditCardStatementText(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	fmt.Println("Credit Card V2 text:", text, "FINISSSSSHHH TEXT")

	lines := strings.Split(text, "\n")

	// Tablo başlığını bul (İşlem tarihi, Açıklama, Taksit, Tutar)
	tableStartIndex := -1

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(strings.ToLower(lines[i]))
		fmt.Printf("CC V2: Checking line %d: %s\n", i, line)

		// Tek satırda yan yana olabilir: "İşlem tarihiAçıklamaTaksitTutar"
		if strings.Contains(line, "işlem tarihi") &&
			strings.Contains(line, "açıklama") &&
			strings.Contains(line, "taksit") &&
			strings.Contains(line, "tutar") {
			// Bu satırda hem başlık hem de işlemler var!
			// Başlık kısmından sonrasını işle
			tableStartIndex = i
			fmt.Printf("CC V2: Found transaction table header (single line) at line %d\n", i)
			fmt.Printf("CC V2: This line contains both header and transactions\n")
			break
		}

		// Alternatif: 4 ardışık satırda da olabilir
		if i < len(lines)-3 {
			if strings.Contains(lines[i], "işlem tarihi") &&
				strings.Contains(strings.ToLower(lines[i+1]), "açıklama") &&
				strings.Contains(strings.ToLower(lines[i+2]), "taksit") &&
				strings.Contains(strings.ToLower(lines[i+3]), "tutar") {
				tableStartIndex = i + 4
				fmt.Printf("CC V2: Found transaction table header (multi-line) starting at line %d\n", i)
				break
			}
		}
	}

	if tableStartIndex == -1 {
		fmt.Println("CC V2: Transaction table header not found")
		fmt.Println("CC V2: Searched for: işlem tarihi, açıklama, taksit, tutar")
		fmt.Println("CC V2: First 20 lines of text:")
		for i := 0; i < len(lines) && i < 20; i++ {
			fmt.Printf("CC V2: Line %d: '%s'\n", i, strings.TrimSpace(lines[i]))
		}
		return entries, nil
	}

	fmt.Printf("CC V2: Processing transactions from line %d onwards\n", tableStartIndex)

	// Tarih regex'i - DD/MM/YYYY veya DD/MM/YY formatı (satır içinde herhangi bir yerde)
	dateRegex := regexp.MustCompile(`(\d{2}/\d{2}/\d{2,4})`)
	// TL tutarı regex'i - sayı + TL formatı (kredi kartında sadece 1 TL var) - case insensitive
	tlAmountRegex := regexp.MustCompile(`(?i)([-+]?\s*[\d.,]+)\s*tl`)

	// Tüm metni temizleyip birleştir
	var cleanText string
	for i := tableStartIndex; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// Gereksiz satırları atla
		lowerLine := strings.ToLower(line)

		// Debug: Hangi satırları atlıyor
		if strings.Contains(lowerLine, "sayfa") && strings.Contains(lowerLine, "/") {
			fmt.Printf("CC V2: Skipping page number: %s\n", line)
			continue // Sayfa numarası
		}
		if strings.Contains(lowerLine, "qnb bank") ||
			strings.Contains(lowerLine, "kart sahibinin") ||
			strings.Contains(lowerLine, "ticaret sicil") ||
			strings.Contains(lowerLine, "mersis") {
			fmt.Printf("CC V2: Skipping header: %s\n", line)
			continue // Sayfa başlıkları
		}

		// İşlem tarihiAçıklamaTaksitTutar başlığını kontrol et
		if strings.Contains(lowerLine, "işlem tarihi") &&
			strings.Contains(lowerLine, "açıklama") &&
			strings.Contains(lowerLine, "taksit") &&
			strings.Contains(lowerLine, "tutar") {
			if cleanText == "" {
				cleanText = line + " " // İlk header'ı ekle
				fmt.Printf("CC V2: Added header: %s\n", line[:min(100, len(line))])
			} else {
				// İkinci sayfada header + işlemler birleşmiş olabilir
				// Header'ı çıkar, işlemleri dahil et
				headerPattern := "işlem tarihiaçıklamataksittutar"
				headerIndex := strings.Index(lowerLine, headerPattern)
				if headerIndex != -1 {
					// Header'dan sonraki kısmı al (işlemler)
					afterHeader := line[headerIndex+len(headerPattern):]
					if strings.TrimSpace(afterHeader) != "" {
						fmt.Printf("CC V2: Extracting transactions from header line: %s\n", afterHeader[:min(100, len(afterHeader))])
						cleanText += afterHeader + " "
					} else {
						fmt.Printf("CC V2: Skipping empty duplicate header\n")
					}
				} else {
					fmt.Printf("CC V2: Skipping duplicate header: %s\n", line[:min(100, len(line))])
				}
			}
			continue
		}

		// Normal satırları ekle
		fmt.Printf("CC V2: Adding line: %s\n", line[:min(100, len(line))])
		cleanText += line + " "
	}

	fmt.Printf("CC V2: Clean text: %s\n", cleanText[:min(500, len(cleanText))])
	entries = s.parseCreditCardTransactionLine(userID, cleanText, dateRegex, tlAmountRegex)

	fmt.Printf("CC V2: Found %d Credit Card transaction entries\n", len(entries))
	return entries, nil
}

// parseCreditCardTransactionLine parses credit card transactions with your exact logic
func (s *service) parseCreditCardTransactionLine(userID, fullText string, dateRegex, tlAmountRegex *regexp.Regexp) []dtos.BankStatementEntry {
	var entries []dtos.BankStatementEntry

	// 1. "İşlem tarihiAçıklamaTaksitTutar" bul
	headerPattern := "işlem tarihiaçıklamataksittutar"
	headerIndex := strings.Index(strings.ToLower(fullText), headerPattern)
	if headerIndex == -1 {
		return entries
	}

	// 2. Header'dan sonraki kısmı al
	afterHeader := fullText[headerIndex+len(headerPattern):]

	// 3. "Bir önceki ekstre bakiyeniz" kısmını atla
	skipPattern := "bir önceki ekstre bakiyeniz"
	skipIndex := strings.Index(strings.ToLower(afterHeader), skipPattern)
	if skipIndex != -1 {
		// "25,00 TL" kısmını da atla
		afterSkip := afterHeader[skipIndex:]
		firstTLIndex := strings.Index(strings.ToLower(afterSkip), "tl")
		if firstTLIndex != -1 {
			afterHeader = afterSkip[firstTLIndex+2:]
		}
	}

	// 4. Şimdi işlemleri parse et
	currentPos := 0

	for {
		// Tarih ara
		dateMatch := dateRegex.FindStringSubmatch(afterHeader[currentPos:])
		if dateMatch == nil {
			break
		}

		date := dateMatch[1]
		datePos := strings.Index(afterHeader[currentPos:], dateMatch[0]) + currentPos

		// Sonraki tarih veya "Sayfa" ara
		nextPos := len(afterHeader)

		// Sonraki tarih ara
		nextDateMatch := dateRegex.FindStringSubmatch(afterHeader[datePos+len(dateMatch[0]):])
		if nextDateMatch != nil {
			nextDatePos := strings.Index(afterHeader[datePos+len(dateMatch[0]):], nextDateMatch[0])
			if nextDatePos != -1 {
				nextPos = datePos + len(dateMatch[0]) + nextDatePos
			}
		}

		// "Sayfa" ara - ama sadece gerçek sayfa sonu ise durdur
		// Tek satırda birleşmiş metinde "Sayfa" normal bir kelime olabilir
		sayfaPos := strings.Index(strings.ToLower(afterHeader[datePos:]), "sayfa")
		if sayfaPos != -1 && datePos+sayfaPos < nextPos {
			// "Sayfa X / Y" formatını kontrol et - gerçek sayfa geçişi mi?
			sayfaText := afterHeader[datePos+sayfaPos : datePos+sayfaPos+min(20, len(afterHeader)-datePos-sayfaPos)]
			if strings.Contains(sayfaText, "/") {
				// Gerçek sayfa numarası, durdur
				nextPos = datePos + sayfaPos
			}
		}

		// "Bir sonraki ekstrenizin tarihi" ara - bu son işlem demektir
		ekstrenizPos := strings.Index(strings.ToLower(afterHeader[datePos:]), "Bir sonraki ekstrenizin")
		if ekstrenizPos != -1 && datePos+ekstrenizPos < nextPos {
			// Son işlem, sadece bu TL'ye kadar al
			nextPos = datePos + ekstrenizPos
		}

		// Bu tarihten sonraki bloğu al
		transactionBlock := afterHeader[datePos:nextPos]

		// Bloktan son TL tutarını bul (geriye doğru)
		tlMatches := tlAmountRegex.FindAllStringSubmatch(transactionBlock, -1)
		if len(tlMatches) == 0 {
			currentPos = nextPos
			continue
		}

		// Son TL tutarını al
		lastTlMatch := tlMatches[len(tlMatches)-1]
		tutar := strings.TrimSpace(lastTlMatch[1])

		// Açıklamayı çıkar: tarihten sonra, son TL'den önceki kısım
		afterDate := transactionBlock[len(dateMatch[0]):]
		tutarIndex := strings.LastIndex(afterDate, lastTlMatch[0])
		if tutarIndex == -1 {
			currentPos = nextPos
			continue
		}

		description := strings.TrimSpace(afterDate[:tutarIndex])

		// Açıklama validasyonu
		if len(description) < 3 {
			currentPos = nextPos
			continue
		}

		// Tutarı parse et
		isNegative := strings.HasPrefix(tutar, "-")
		tutarClean := strings.TrimPrefix(tutar, "-")
		tutarClean = strings.TrimPrefix(tutarClean, "+")
		tutarClean = strings.ReplaceAll(tutarClean, " ", "")
		tutarClean = strings.ReplaceAll(tutarClean, ".", "")
		tutarClean = strings.ReplaceAll(tutarClean, ",", ".")

		amount, err := decimal.NewFromString(tutarClean)
		if err != nil {
			currentPos = nextPos
			continue
		}

		// İşlem tipini belirle
		transactionType := "expense"
		lowerDesc := strings.ToLower(description)
		if isNegative || strings.Contains(lowerDesc, "ödeme") || strings.Contains(lowerDesc, "payment") {
			transactionType = "income"
		}

		// Tarih formatını düzenle
		formattedDate := strings.ReplaceAll(date, "/", ".")
		if len(strings.Split(formattedDate, ".")[2]) == 2 {
			parts := strings.Split(formattedDate, ".")
			formattedDate = parts[0] + "." + parts[1] + ".20" + parts[2]
		}

		// Kategori ve hesap ID'lerini ata
		categoryID := s.getCategoryIDForTransaction(userID, description, transactionType)
		accountID := s.getAccountIDForTransaction(userID, description)

		entry := dtos.BankStatementEntry{
			Date:        formattedDate,
			Description: strings.TrimSpace(description),
			Amount:      amount.InexactFloat64(),
			Type:        transactionType,
			CategoryID:  categoryID,
			AccountID:   accountID,
		}
		entries = append(entries, entry)

		// Sonraki pozisyona geç
		currentPos = nextPos

		// Artık sayfa geçişi yok, temiz tek satır var - devam et
	}

	return entries
}

// min helper function
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// ParseGarantiStatement parses a Garanti Bank statement PDF and extracts transactions
func (s *service) ParseGarantiStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, fmt.Errorf("pdf dosyası okunamadı: %w", err)
	}

	// PDF dökümanını aç
	pdfReader, err := pdf.NewReader(bytes.NewReader(buf.Bytes()), int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("pdf okunamadı: %w", err)
	}

	// Tüm sayfalardaki metni birleştir
	var allText string
	numPages := pdfReader.NumPage()
	for i := 1; i <= numPages; i++ {
		page := pdfReader.Page(i)
		if page.V.IsNull() {
			continue
		}

		// Sayfadaki metni al
		pageText, err := page.GetPlainText(nil)
		if err != nil {
			fmt.Printf("Error extracting text from page %d: %v\n", i, err)
			// Eğer GetPlainText çalışmazsa, alternatif yöntem deneyelim
			content := page.Content()
			if content.Text != nil && len(content.Text) > 0 {
				for _, text := range content.Text {
					allText += text.S
				}
			}
			continue
		}

		allText += pageText + "\n"
	}

	// PDF içeriğini alıp işleme fonksiyonuna gönder
	return s.parseGarantiStatementText(userID, allText)
}

// parseGarantiStatementText parses the extracted text from a Garanti Bank statement
func (s *service) parseGarantiStatementText(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	// Split text into lines and clean them
	lines := strings.Split(text, "\n")
	var cleanLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}

	// Garanti ekstresine özel regex patternleri
	// Garanti'de genellikle tarih formatı: DD.MM.YYYY veya DD/MM/YYYY
	// Miktar formatı: 1.234,56 TL veya 1,234.56 gibi olabilir

	// Pattern 1: Tarih Açıklama Miktar formatı
	// Örnek: "15.11.2024 MARKET ALIŞVERİŞİ 125,50 TL"
	transactionRegex1 := regexp.MustCompile(`(\d{2}[./]\d{2}[./]\d{4})\s+([^0-9]+?)\s+(\d{1,3}(?:[.,]\d{3})*[.,]\d{2})\s*TL`)

	// Pattern 2: Daha detaylı format - tarih, saat, açıklama, miktar
	// Örnek: "15.11.2024 14:30 GARANTI BANKASI ATM 100,00 TL"
	transactionRegex2 := regexp.MustCompile(`(\d{2}[./]\d{2}[./]\d{4})\s+(\d{2}:\d{2})\s+([^0-9]+?)\s+(\d{1,3}(?:[.,]\d{3})*[.,]\d{2})\s*TL`)

	// Tüm text'i birleştirip pattern matching yapalım
	fullText := strings.Join(cleanLines, " ")

	// İlk pattern ile deneme
	matches1 := transactionRegex1.FindAllStringSubmatch(fullText, -1)
	for _, match := range matches1 {
		if len(match) == 4 {
			entry := s.processGarantiMatch(userID, match[1], "", match[2], match[3])
			if entry != nil {
				entries = append(entries, *entry)
			}
		}
	}

	// İkinci pattern ile deneme
	matches2 := transactionRegex2.FindAllStringSubmatch(fullText, -1)
	for _, match := range matches2 {
		if len(match) == 5 {
			entry := s.processGarantiMatch(userID, match[1], match[2], match[3], match[4])
			if entry != nil {
				entries = append(entries, *entry)
			}
		}
	}

	// Eğer regex pattern'ları yeterli değilse, satır satır işleme
	if len(entries) == 0 {
		entries = s.parseGarantiLineByLine(userID, cleanLines)
	}

	fmt.Printf("Found %d Garanti transaction entries\n", len(entries))
	return entries, nil
}

// processGarantiMatch processes a regex match and creates a BankStatementEntry
func (s *service) processGarantiMatch(userID, date, time, description, amountStr string) *dtos.BankStatementEntry {
	// Tarih formatını standardize et
	formattedDate := strings.ReplaceAll(date, "/", ".")
	if time != "" {
		formattedDate += " " + time
	}

	// Açıklamayı temizle
	fullDescription := strings.TrimSpace(description)

	// Garanti'ye özel açıklama temizleme
	fullDescription = strings.ReplaceAll(fullDescription, "GARANTI BANKASI", "")
	fullDescription = strings.ReplaceAll(fullDescription, "GARANTİ BANKASI", "")
	fullDescription = strings.TrimSpace(fullDescription)

	// Miktarı parse et
	amountFloat, err := s.parseGarantiAmount(amountStr)
	if err != nil {
		fmt.Printf("Error parsing amount %s: %v\n", amountStr, err)
		return nil
	}

	// İşlem tipini belirle (Garanti'de negatif tutarlar çıkış, pozitif tutarlar giriş)
	transactionType := "expense"
	if amountFloat > 0 {
		// Bazı gelir belirteçleri
		lowerDesc := strings.ToLower(fullDescription)
		if strings.Contains(lowerDesc, "gelen") ||
			strings.Contains(lowerDesc, "transfer") ||
			strings.Contains(lowerDesc, "eft") ||
			strings.Contains(lowerDesc, "havale") ||
			strings.Contains(lowerDesc, "maaş") ||
			strings.Contains(lowerDesc, "salary") {
			transactionType = "income"
		}
	}

	// Kategori ve hesap ID'lerini otomatik ata
	categoryID := s.getCategoryIDForTransaction(userID, fullDescription, transactionType)
	accountID := s.getAccountIDForTransaction(userID, fullDescription)

	return &dtos.BankStatementEntry{
		Date:        formattedDate,
		Description: fullDescription,
		Amount:      math.Abs(amountFloat),
		Type:        transactionType,
		CategoryID:  categoryID,
		AccountID:   accountID,
	}
}

// parseGarantiAmount parses Garanti Bank amount format
func (s *service) parseGarantiAmount(amountStr string) (float64, error) {
	// Garanti'de miktar formatları:
	// 1.234,56 TL
	// 1,234.56 TL
	// 1234,56 TL
	// 1234.56 TL

	// TL'yi kaldır
	amountClean := strings.ReplaceAll(amountStr, "TL", "")
	amountClean = strings.TrimSpace(amountClean)

	// Negatif kontrol
	isNegative := strings.HasPrefix(amountClean, "-")
	if isNegative {
		amountClean = strings.TrimPrefix(amountClean, "-")
	}

	// Türk formatı kontrolü (1.234,56)
	if strings.Contains(amountClean, ",") && strings.LastIndex(amountClean, ",") > strings.LastIndex(amountClean, ".") {
		// Türk formatı: binlik ayırıcı nokta, ondalık ayırıcı virgül
		amountClean = strings.ReplaceAll(amountClean, ".", "")  // Binlik ayırıcıları kaldır
		amountClean = strings.ReplaceAll(amountClean, ",", ".") // Ondalık ayırıcıyı değiştir
	} else if strings.Contains(amountClean, ".") && strings.LastIndex(amountClean, ".") > strings.LastIndex(amountClean, ",") {
		// İngiliz formatı: binlik ayırıcı virgül, ondalık ayırıcı nokta
		amountClean = strings.ReplaceAll(amountClean, ",", "") // Binlik ayırıcıları kaldır
	}

	amount, err := strconv.ParseFloat(amountClean, 64)
	if err != nil {
		return 0, err
	}

	if isNegative {
		amount = -amount
	}

	return amount, nil
}

// parseGarantiLineByLine parses Garanti statement line by line when regex fails
func (s *service) parseGarantiLineByLine(userID string, lines []string) []dtos.BankStatementEntry {
	var entries []dtos.BankStatementEntry

	dateRegex := regexp.MustCompile(`^\d{2}[./]\d{2}[./]\d{4}$`)
	timeRegex := regexp.MustCompile(`^\d{2}:\d{2}$`)
	amountRegex := regexp.MustCompile(`^-?(\d{1,3}(?:[.,]\d{3})*[.,]\d{2})\s*TL?$`)

	i := 0
	for i < len(lines) {
		line := lines[i]

		// Tarih satırı bulduğumuzda işleme başla
		if dateRegex.MatchString(line) {
			date := line
			var timeVal string
			var descriptionLines []string
			var amountStr string

			j := i + 1

			// Sonraki satırda saat var mı kontrol et
			if j < len(lines) && timeRegex.MatchString(lines[j]) {
				timeVal = lines[j]
				j++
			}

			// Açıklama ve miktar satırlarını topla
			for j < len(lines) {
				if dateRegex.MatchString(lines[j]) {
					// Yeni tarih bulundu, burada dur
					break
				}

				// Miktar satırı mı kontrol et
				if amountRegex.MatchString(lines[j]) {
					amountStr = lines[j]
					j++
					break
				}

				// Açıklama satırı
				descriptionLines = append(descriptionLines, lines[j])
				j++
			}

			// Eğer miktar bulunmazsa sonraki satırlarda ara
			if amountStr == "" {
				for k := j; k < len(lines) && k < j+3; k++ {
					if amountRegex.MatchString(lines[k]) {
						amountStr = lines[k]
						break
					}
				}
			}

			if amountStr != "" {
				// Açıklamayı birleştir
				fullDescription := strings.Join(descriptionLines, " ")
				fullDescription = strings.TrimSpace(fullDescription)

				// İşlemi oluştur
				formattedDate := strings.ReplaceAll(date, "/", ".")
				if timeVal != "" {
					formattedDate += " " + timeVal
				}

				entry := s.processGarantiMatch(userID, date, timeVal, fullDescription, amountStr)
				if entry != nil {
					entries = append(entries, *entry)
				}
			}

			i = j
		} else {
			i++
		}
	}

	return entries
}

// findCategoryByName finds a category by name from the given categories
func (s *service) findCategoryByName(categories []dtos.CategoryResponse, keywords []string) string {
	// İlk önce tam eşleşme ara
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)
		for _, keyword := range keywords {
			keywordLower := strings.ToLower(keyword)
			if categoryNameLower == keywordLower {
				return category.ID
			}
		}
	}

	// Sonra kısmi eşleşme ara
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)
		for _, keyword := range keywords {
			keywordLower := strings.ToLower(keyword)
			if strings.Contains(categoryNameLower, keywordLower) || strings.Contains(keywordLower, categoryNameLower) {
				return category.ID
			}
		}
	}

	// Eğer hiç eşleşme bulunamazsa boş string döndür
	return ""
}

// Helper functions for default category and account assignment
func (s *service) getDefaultExpenseCategoryID(userID string) string {
	// Get all expense categories for the user
	categories, err := s.categoryService.GetAllCategories(userID, "expense")
	if err != nil || len(categories) == 0 {
		return ""
	}

	// Look for a general expense category first
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)
		if strings.Contains(categoryNameLower, "genel") ||
			strings.Contains(categoryNameLower, "diğer") ||
			strings.Contains(categoryNameLower, "other") ||
			strings.Contains(categoryNameLower, "general") ||
			strings.Contains(categoryNameLower, "gider") ||
			strings.Contains(categoryNameLower, "expense") {
			return category.ID
		}
	}

	// If no general category found, return the first expense category
	return categories[0].ID
}

func (s *service) getDefaultIncomeCategoryID(userID string) string {
	// Get all income categories for the user
	categories, err := s.categoryService.GetAllCategories(userID, "income")
	if err != nil || len(categories) == 0 {
		return ""
	}

	// Look for a general income category first
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)
		if strings.Contains(categoryNameLower, "genel") ||
			strings.Contains(categoryNameLower, "diğer") ||
			strings.Contains(categoryNameLower, "other") ||
			strings.Contains(categoryNameLower, "general") ||
			strings.Contains(categoryNameLower, "gelir") ||
			strings.Contains(categoryNameLower, "income") {
			return category.ID
		}
	}

	// If no general category found, return the first income category
	return categories[0].ID
}

func (s *service) getDefaultAccountID(userID string) string {
	// Get all accounts for the user
	accounts, err := s.accountService.GetAllAccounts(userID)
	if err != nil || len(accounts) == 0 {
		return ""
	}

	// Return the first account as default
	return accounts[0].ID
}
