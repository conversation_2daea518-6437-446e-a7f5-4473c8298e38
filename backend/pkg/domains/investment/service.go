package investment

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
)

type Service interface {
	SimulateInvestment(userID string, req *dtos.InvestmentSimulateRequest) (*dtos.InvestmentSimulateResponse, error)
	WhatIfSimulation(userID string, req *dtos.InvestmentWhatIfRequest) (*dtos.InvestmentWhatIfResponse, error)
	GetUserSimulations(userID string, page int, limit int) ([]dtos.InvestmentSimulateResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) SimulateInvestment(userID string, req *dtos.InvestmentSimulateRequest) (*dtos.InvestmentSimulateResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse start date
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, errors.New("invalid start date format, use YYYY-MM-DD")
	}

	// Get historical price for start date
	priceAtStart, err := s.getHistoricalPrice(req.Asset, startDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical price: %w", err)
	}

	// Get current price
	currentPrice, err := s.getCurrentPrice(req.Asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get current price: %w", err)
	}

	// Create simulation entity
	simulation := &entities.InvestmentSimulation{
		UserID:         userUUID,
		Asset:          req.Asset,
		AmountInvested: req.Amount,
		StartDate:      startDate,
		PriceAtStart:   priceAtStart,
		CurrentPrice:   currentPrice,
		SimulationType: "simulate",
	}

	// Calculate investment metrics
	simulation.CalculateInvestment()

	// Save to database
	if err := s.repository.Create(simulation); err != nil {
		return nil, fmt.Errorf("failed to save simulation: %w", err)
	}

	// Return response
	return &dtos.InvestmentSimulateResponse{
		Asset:             simulation.Asset,
		AmountInvested:    simulation.AmountInvested,
		StartDate:         simulation.StartDate.Format("2006-01-02"),
		PriceAtStart:      simulation.PriceAtStart,
		CurrentPrice:      simulation.CurrentPrice,
		UnitsBought:       simulation.UnitsBought,
		CurrentValue:      simulation.CurrentValue,
		Profit:            simulation.Profit,
		GrowthRatePercent: simulation.GrowthRatePercent,
	}, nil
}

func (s *service) WhatIfSimulation(userID string, req *dtos.InvestmentWhatIfRequest) (*dtos.InvestmentWhatIfResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse hypothetical date
	hypotheticalDate, err := time.Parse("2006-01-02", req.HypotheticalDate)
	if err != nil {
		return nil, errors.New("invalid hypothetical date format, use YYYY-MM-DD")
	}

	// Get historical price for hypothetical date
	priceThen, err := s.getHistoricalPrice(req.Asset, hypotheticalDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical price: %w", err)
	}

	// Get current price
	priceNow, err := s.getCurrentPrice(req.Asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get current price: %w", err)
	}

	// Create simulation entity
	simulation := &entities.InvestmentSimulation{
		UserID:           userUUID,
		Asset:            req.Asset,
		AmountInvested:   req.Amount,
		StartDate:        hypotheticalDate,
		SimulationType:   "whatif",
		HypotheticalDate: &hypotheticalDate,
		PriceThen:        &priceThen,
		PriceNow:         &priceNow,
	}

	// Calculate what-if metrics
	simulation.CalculateWhatIf()

	// Save to database
	if err := s.repository.Create(simulation); err != nil {
		return nil, fmt.Errorf("failed to save simulation: %w", err)
	}

	// Return response
	return &dtos.InvestmentWhatIfResponse{
		Asset:             simulation.Asset,
		HypotheticalDate:  simulation.HypotheticalDate.Format("2006-01-02"),
		AmountInvested:    simulation.AmountInvested,
		PriceThen:         *simulation.PriceThen,
		PriceNow:          *simulation.PriceNow,
		CurrentValue:      simulation.CurrentValue,
		Profit:            simulation.Profit,
		GrowthRatePercent: simulation.GrowthRatePercent,
	}, nil
}

func (s *service) GetUserSimulations(userID string, page int, limit int) ([]dtos.InvestmentSimulateResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get simulations from repository
	simulations, err := s.repository.FindByUserID(userUUID, limit, offset)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responses []dtos.InvestmentSimulateResponse
	for _, sim := range simulations {
		response := dtos.InvestmentSimulateResponse{
			Asset:             sim.Asset,
			AmountInvested:    sim.AmountInvested,
			StartDate:         sim.StartDate.Format("2006-01-02"),
			PriceAtStart:      sim.PriceAtStart,
			CurrentPrice:      sim.CurrentPrice,
			UnitsBought:       sim.UnitsBought,
			CurrentValue:      sim.CurrentValue,
			Profit:            sim.Profit,
			GrowthRatePercent: sim.GrowthRatePercent,
		}
		responses = append(responses, response)
	}

	return responses, nil
}

// getHistoricalPrice gets historical price for an asset on a specific date
func (s *service) getHistoricalPrice(asset string, date time.Time) (float64, error) {
	// Check if it's a crypto asset
	if s.isCryptoAsset(asset) {
		return s.getCryptoHistoricalPrice(asset, date)
	}

	// For other assets, use Yahoo Finance
	return s.getYahooHistoricalPrice(asset, date)
}

// getCurrentPrice gets current price for an asset
func (s *service) getCurrentPrice(asset string) (float64, error) {
	// Check if it's a crypto asset
	if s.isCryptoAsset(asset) {
		return s.getCryptoCurrentPrice(asset)
	}

	// For other assets, use Yahoo Finance
	return s.getYahooCurrentPrice(asset)
}

// isCryptoAsset checks if the asset is a cryptocurrency
func (s *service) isCryptoAsset(asset string) bool {
	cryptoAssets := []string{"BTC", "ETH", "ADA", "DOT", "LINK", "LTC", "XRP", "BCH", "BNB", "DOGE"}
	for _, crypto := range cryptoAssets {
		if strings.ToUpper(asset) == crypto {
			return true
		}
	}
	return false
}

// getCryptoHistoricalPrice gets historical crypto price from CoinGecko
func (s *service) getCryptoHistoricalPrice(asset string, date time.Time) (float64, error) {
	// Map asset symbols to CoinGecko IDs
	assetMap := map[string]string{
		"BTC":  "bitcoin",
		"ETH":  "ethereum",
		"ADA":  "cardano",
		"DOT":  "polkadot",
		"LINK": "chainlink",
		"LTC":  "litecoin",
		"XRP":  "ripple",
		"BCH":  "bitcoin-cash",
		"BNB":  "binancecoin",
		"DOGE": "dogecoin",
	}

	coinID, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		return 0, fmt.Errorf("unsupported crypto asset: %s", asset)
	}

	// Format date for CoinGecko API (DD-MM-YYYY)
	dateStr := date.Format("02-01-2006")

	// CoinGecko API endpoint for historical data
	url := fmt.Sprintf("https://api.coingecko.com/api/v3/coins/%s/history?date=%s", coinID, dateStr)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from CoinGecko: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("CoinGecko API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var historyResp dtos.CoinGeckoHistoryResponse
	if err := json.Unmarshal(body, &historyResp); err != nil {
		return 0, fmt.Errorf("failed to parse CoinGecko response: %w", err)
	}

	// Get USD price
	price, exists := historyResp.MarketData.CurrentPrice["usd"]
	if !exists {
		return 0, fmt.Errorf("USD price not found in CoinGecko response")
	}

	return price, nil
}

// getCryptoCurrentPrice gets current crypto price from CoinGecko
func (s *service) getCryptoCurrentPrice(asset string) (float64, error) {
	// Map asset symbols to CoinGecko IDs
	assetMap := map[string]string{
		"BTC":  "bitcoin",
		"ETH":  "ethereum",
		"ADA":  "cardano",
		"DOT":  "polkadot",
		"LINK": "chainlink",
		"LTC":  "litecoin",
		"XRP":  "ripple",
		"BCH":  "bitcoin-cash",
		"BNB":  "binancecoin",
		"DOGE": "dogecoin",
	}

	coinID, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		return 0, fmt.Errorf("unsupported crypto asset: %s", asset)
	}

	// CoinGecko API endpoint for current price
	url := fmt.Sprintf("https://api.coingecko.com/api/v3/simple/price?ids=%s&vs_currencies=usd", coinID)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from CoinGecko: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("CoinGecko API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var priceResp dtos.CoinGeckoPriceResponse
	if err := json.Unmarshal(body, &priceResp); err != nil {
		return 0, fmt.Errorf("failed to parse CoinGecko response: %w", err)
	}

	// Get USD price
	coinData, exists := priceResp[coinID]
	if !exists {
		return 0, fmt.Errorf("coin data not found in CoinGecko response")
	}

	price, exists := coinData["usd"]
	if !exists {
		return 0, fmt.Errorf("USD price not found in CoinGecko response")
	}

	return price, nil
}

// getYahooHistoricalPrice gets historical price from Yahoo Finance
func (s *service) getYahooHistoricalPrice(asset string, date time.Time) (float64, error) {
	// Map asset symbols to Yahoo Finance symbols
	assetMap := map[string]string{
		"GOLD":    "GC=F",     // Gold futures
		"USDTRY":  "USDTRY=X", // USD/TRY exchange rate
		"BIST100": "XU100.IS", // BIST 100 index
		"SPY":     "SPY",      // S&P 500 ETF
		"QQQ":     "QQQ",      // NASDAQ ETF
	}

	symbol, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		// If not in map, use the asset as-is (might be a stock symbol)
		symbol = strings.ToUpper(asset)
	}

	// Calculate timestamps
	startTime := date.Unix()
	endTime := date.AddDate(0, 0, 1).Unix() // Next day to ensure we get the date

	// Yahoo Finance API endpoint
	url := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?period1=%d&period2=%d&interval=1d",
		symbol, startTime, endTime)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from Yahoo Finance: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("Yahoo Finance API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var yahooResp dtos.YahooFinanceResponse
	if err := json.Unmarshal(body, &yahooResp); err != nil {
		return 0, fmt.Errorf("failed to parse Yahoo Finance response: %w", err)
	}

	if len(yahooResp.Chart.Result) == 0 {
		return 0, fmt.Errorf("no data found for asset %s on date %s", asset, date.Format("2006-01-02"))
	}

	result := yahooResp.Chart.Result[0]
	if len(result.Indicators.Quote) == 0 || len(result.Indicators.Quote[0].Close) == 0 {
		return 0, fmt.Errorf("no price data found for asset %s on date %s", asset, date.Format("2006-01-02"))
	}

	// Get the closing price
	price := result.Indicators.Quote[0].Close[0]
	if price == 0 {
		return 0, fmt.Errorf("invalid price data for asset %s on date %s", asset, date.Format("2006-01-02"))
	}

	return price, nil
}

// getYahooCurrentPrice gets current price from Yahoo Finance
func (s *service) getYahooCurrentPrice(asset string) (float64, error) {
	// Map asset symbols to Yahoo Finance symbols
	assetMap := map[string]string{
		"GOLD":    "GC=F",     // Gold futures
		"USDTRY":  "USDTRY=X", // USD/TRY exchange rate
		"BIST100": "XU100.IS", // BIST 100 index
		"SPY":     "SPY",      // S&P 500 ETF
		"QQQ":     "QQQ",      // NASDAQ ETF
	}

	symbol, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		// If not in map, use the asset as-is (might be a stock symbol)
		symbol = strings.ToUpper(asset)
	}

	// Yahoo Finance API endpoint for current data
	url := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s", symbol)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from Yahoo Finance: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("Yahoo Finance API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var yahooResp dtos.YahooFinanceResponse
	if err := json.Unmarshal(body, &yahooResp); err != nil {
		return 0, fmt.Errorf("failed to parse Yahoo Finance response: %w", err)
	}

	if len(yahooResp.Chart.Result) == 0 {
		return 0, fmt.Errorf("no data found for asset %s", asset)
	}

	result := yahooResp.Chart.Result[0]

	// Try to get the regular market price from meta first
	if result.Meta.RegularMarketPrice > 0 {
		return result.Meta.RegularMarketPrice, nil
	}

	// Fallback to the latest close price
	if len(result.Indicators.Quote) > 0 && len(result.Indicators.Quote[0].Close) > 0 {
		prices := result.Indicators.Quote[0].Close
		// Get the last non-zero price
		for i := len(prices) - 1; i >= 0; i-- {
			if prices[i] > 0 {
				return prices[i], nil
			}
		}
	}

	return 0, fmt.Errorf("no current price data found for asset %s", asset)
}
