package investment

import (
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(simulation *entities.InvestmentSimulation) error
	FindByID(id uuid.UUID) (*entities.InvestmentSimulation, error)
	FindByUserID(userID uuid.UUID, limit int, offset int) ([]entities.InvestmentSimulation, error)
	Update(simulation *entities.InvestmentSimulation) error
	Delete(id uuid.UUID) error
	GetTotalCountByUserID(userID uuid.UUID) (int64, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(simulation *entities.InvestmentSimulation) error {
	return r.db.Create(simulation).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.InvestmentSimulation, error) {
	var simulation entities.InvestmentSimulation
	err := r.db.Where("id = ?", id).First(&simulation).Error
	if err != nil {
		return nil, err
	}
	return &simulation, nil
}

func (r *repository) FindByUserID(userID uuid.UUID, limit int, offset int) ([]entities.InvestmentSimulation, error) {
	var simulations []entities.InvestmentSimulation
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&simulations).Error
	if err != nil {
		return nil, err
	}
	return simulations, nil
}

func (r *repository) Update(simulation *entities.InvestmentSimulation) error {
	return r.db.Save(simulation).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.InvestmentSimulation{}, id).Error
}

func (r *repository) GetTotalCountByUserID(userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.Model(&entities.InvestmentSimulation{}).
		Where("user_id = ?", userID).
		Count(&count).Error
	return count, err
}
