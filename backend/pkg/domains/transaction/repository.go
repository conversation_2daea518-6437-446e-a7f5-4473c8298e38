package transaction

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	Create(transaction *entities.Transaction) error
	FindByID(id uuid.UUID) (*entities.Transaction, error)
	Update(transaction *entities.Transaction) error
	Delete(id uuid.UUID) error
	FindAll(userID uuid.UUID, filter *dtos.TransactionFilterRequest) ([]entities.Transaction, error)
	GetTotalCount(userID uuid.UUID, filter *dtos.TransactionFilterRequest) (int64, error)
	GetSummary(userID uuid.UUID, filter *dtos.TransactionFilterRequest) (*dtos.TransactionSummary, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(transaction *entities.Transaction) error {
	return r.db.Create(transaction).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.Transaction, error) {
	var transaction entities.Transaction
	if err := r.db.Where("id = ?", id).First(&transaction).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("transaction not found")
		}
		return nil, err
	}
	return &transaction, nil
}

func (r *repository) Update(transaction *entities.Transaction) error {
	return r.db.Save(transaction).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Transaction{}, "id = ?", id).Error
}

func (r *repository) FindAll(userID uuid.UUID, filter *dtos.TransactionFilterRequest) ([]entities.Transaction, error) {
	var transactions []entities.Transaction
	query := r.db.Where("user_id = ?", userID)

	// Apply filters
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}

	if filter.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", filter.StartDate)
		if err == nil {
			query = query.Where("transaction_date >= ?", startDate)
		}
	}

	if filter.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", filter.EndDate)
		if err == nil {
			// Add one day to include the end date
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("transaction_date < ?", endDate)
		}
	}

	if filter.CategoryID != "" {
		query = query.Where("category_id = ?", filter.CategoryID)
	}

	if filter.PaymentMethod != "" {
		query = query.Where("payment_method = ?", filter.PaymentMethod)
	}

	if filter.AccountID != "" {
		query = query.Where("account_id = ?", filter.AccountID)
	}

	if filter.MinAmount > 0 {
		query = query.Where("amount >= ?", filter.MinAmount)
	}

	if filter.MaxAmount > 0 {
		query = query.Where("amount <= ?", filter.MaxAmount)
	}

	if filter.Search != "" {
		query = query.Where("title ILIKE ? OR note ILIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	// Pagination
	limit := 10
	if filter.Limit > 0 {
		limit = filter.Limit
	}

	page := 1
	if filter.Page > 0 {
		page = filter.Page
	}

	offset := (page - 1) * limit

	if err := query.Debug().Order("transaction_date DESC").Limit(limit).Offset(offset).Find(&transactions).Error; err != nil {
		return nil, err
	}

	return transactions, nil
}

func (r *repository) GetTotalCount(userID uuid.UUID, filter *dtos.TransactionFilterRequest) (int64, error) {
	var count int64
	query := r.db.Model(&entities.Transaction{}).Where("user_id = ?", userID)

	// Apply the same filters as FindAll
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}

	if filter.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", filter.StartDate)
		if err == nil {
			query = query.Where("transaction_date >= ?", startDate)
		}
	}

	if filter.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", filter.EndDate)
		if err == nil {
			// Add one day to include the end date
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("transaction_date < ?", endDate)
		}
	}

	if filter.CategoryID != "" {
		query = query.Where("category_id = ?", filter.CategoryID)
	}

	if filter.PaymentMethod != "" {
		query = query.Where("payment_method = ?", filter.PaymentMethod)
	}

	if filter.AccountID != "" {
		query = query.Where("account_id = ?", filter.AccountID)
	}

	if filter.MinAmount > 0 {
		query = query.Where("amount >= ?", filter.MinAmount)
	}

	if filter.MaxAmount > 0 {
		query = query.Where("amount <= ?", filter.MaxAmount)
	}

	if filter.Search != "" {
		query = query.Where("title ILIKE ? OR note ILIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *repository) GetSummary(userID uuid.UUID, filter *dtos.TransactionFilterRequest) (*dtos.TransactionSummary, error) {
	var summary dtos.TransactionSummary

	// Build base query with same filters as FindAll and GetTotalCount
	query := r.db.Model(&entities.Transaction{}).Where("user_id = ?", userID)

	// Apply the same filters
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}

	if filter.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", filter.StartDate)
		if err == nil {
			query = query.Where("transaction_date >= ?", startDate)
		}
	}

	if filter.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", filter.EndDate)
		if err == nil {
			// Add one day to include the end date
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("transaction_date < ?", endDate)
		}
	}

	if filter.CategoryID != "" {
		query = query.Where("category_id = ?", filter.CategoryID)
	}

	if filter.PaymentMethod != "" {
		query = query.Where("payment_method = ?", filter.PaymentMethod)
	}

	if filter.AccountID != "" {
		query = query.Where("account_id = ?", filter.AccountID)
	}

	if filter.MinAmount > 0 {
		query = query.Where("amount >= ?", filter.MinAmount)
	}

	if filter.MaxAmount > 0 {
		query = query.Where("amount <= ?", filter.MaxAmount)
	}

	if filter.Search != "" {
		query = query.Where("title ILIKE ? OR note ILIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	// Calculate summary using SQL aggregation
	var result struct {
		TotalIncome   float64 `gorm:"column:total_income"`
		TotalExpenses float64 `gorm:"column:total_expenses"`
		Count         int64   `gorm:"column:count"`
	}

	err := query.Select(`
		COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
		COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
		COUNT(*) as count
	`).Scan(&result).Error

	if err != nil {
		return nil, err
	}

	summary.TotalIncome = result.TotalIncome
	summary.TotalExpenses = result.TotalExpenses
	summary.NetAmount = result.TotalIncome - result.TotalExpenses
	summary.Count = result.Count

	return &summary, nil
}
