package dtos

import (
	"time"
)

// InvestmentSimulateRequest represents the request for investment simulation
type InvestmentSimulateRequest struct {
	Asset     string `json:"asset" binding:"required"`     // BTC, ETH, GOLD, USDTRY, BIST100, etc.
	Amount    float64 `json:"amount" binding:"required,gt=0"` // Investment amount
	StartDate string `json:"start_date" binding:"required"` // Start date in YYYY-MM-DD format
}

// InvestmentSimulateResponse represents the response for investment simulation
type InvestmentSimulateResponse struct {
	Asset               string  `json:"asset"`
	AmountInvested      float64 `json:"amount_invested"`
	StartDate           string  `json:"start_date"`
	PriceAtStart        float64 `json:"price_at_start"`
	CurrentPrice        float64 `json:"current_price"`
	UnitsBought         float64 `json:"units_bought"`
	CurrentValue        float64 `json:"current_value"`
	Profit              float64 `json:"profit"`
	GrowthRatePercent   float64 `json:"growth_rate_percent"`
}

// InvestmentWhatIfRequest represents the request for what-if simulation
type InvestmentWhatIfRequest struct {
	Asset            string  `json:"asset" binding:"required"`     // GOLD, BTC, ETH, etc.
	Amount           float64 `json:"amount" binding:"required,gt=0"` // Investment amount
	HypotheticalDate string  `json:"hypothetical_date" binding:"required"` // Date in YYYY-MM-DD format
}

// InvestmentWhatIfResponse represents the response for what-if simulation
type InvestmentWhatIfResponse struct {
	Asset            string  `json:"asset"`
	HypotheticalDate string  `json:"hypothetical_date"`
	AmountInvested   float64 `json:"amount_invested"`
	PriceThen        float64 `json:"price_then"`
	PriceNow         float64 `json:"price_now"`
	CurrentValue     float64 `json:"current_value"`
	Profit           float64 `json:"profit"`
	GrowthRatePercent float64 `json:"growth_rate_percent"`
}

// AssetPriceData represents price data from external APIs
type AssetPriceData struct {
	Asset     string    `json:"asset"`
	Price     float64   `json:"price"`
	Date      time.Time `json:"date"`
	Currency  string    `json:"currency"`
}

// CoinGeckoHistoryResponse represents CoinGecko API history response
type CoinGeckoHistoryResponse struct {
	ID          string                 `json:"id"`
	Symbol      string                 `json:"symbol"`
	Name        string                 `json:"name"`
	MarketData  CoinGeckoMarketData    `json:"market_data"`
}

// CoinGeckoMarketData represents market data from CoinGecko
type CoinGeckoMarketData struct {
	CurrentPrice map[string]float64 `json:"current_price"`
}

// CoinGeckoPriceResponse represents CoinGecko current price response
type CoinGeckoPriceResponse map[string]map[string]float64

// YahooFinanceResponse represents Yahoo Finance API response
type YahooFinanceResponse struct {
	Chart YahooChart `json:"chart"`
}

// YahooChart represents Yahoo Finance chart data
type YahooChart struct {
	Result []YahooResult `json:"result"`
	Error  interface{}   `json:"error"`
}

// YahooResult represents Yahoo Finance result data
type YahooResult struct {
	Meta       YahooMeta       `json:"meta"`
	Timestamp  []int64         `json:"timestamp"`
	Indicators YahooIndicators `json:"indicators"`
}

// YahooMeta represents Yahoo Finance metadata
type YahooMeta struct {
	Currency             string  `json:"currency"`
	Symbol               string  `json:"symbol"`
	ExchangeName         string  `json:"exchangeName"`
	InstrumentType       string  `json:"instrumentType"`
	FirstTradeDate       int64   `json:"firstTradeDate"`
	RegularMarketTime    int64   `json:"regularMarketTime"`
	Gmtoffset            int     `json:"gmtoffset"`
	Timezone             string  `json:"timezone"`
	ExchangeTimezoneName string  `json:"exchangeTimezoneName"`
	RegularMarketPrice   float64 `json:"regularMarketPrice"`
	ChartPreviousClose   float64 `json:"chartPreviousClose"`
}

// YahooIndicators represents Yahoo Finance indicators
type YahooIndicators struct {
	Quote []YahooQuote `json:"quote"`
}

// YahooQuote represents Yahoo Finance quote data
type YahooQuote struct {
	Open   []float64 `json:"open"`
	High   []float64 `json:"high"`
	Low    []float64 `json:"low"`
	Close  []float64 `json:"close"`
	Volume []int64   `json:"volume"`
}
