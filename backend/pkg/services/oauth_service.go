package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/utils"
	"gorm.io/gorm"
)

type OAuthService struct {
	db *gorm.DB
}

func NewOAuthService(db *gorm.DB) *OAuthService {
	return &OAuthService{
		db: db,
	}
}

// Google OAuth Authentication
func (s *OAuthService) AuthenticateWithGoogle(req *dtos.GoogleOAuthRequest) (*dtos.OAuthResponse, error) {
	// Verify Google ID Token
	googleUser, err := s.verifyGoogleIDToken(req.IDToken)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Invalid Google ID token",
		}, err
	}

	// Check if user exists with Google ID
	var user entities.User
	result := s.db.Where("email = ?", googleUser.Email).Debug().First(&user)

	isNewUser := false
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Check if user exists with same email
			emailResult := s.db.Where("email = ?", googleUser.Email).First(&user)
			if emailResult.Error != nil {
				if errors.Is(emailResult.Error, gorm.ErrRecordNotFound) {
					// Create new user
					user, err = s.createGoogleUser(googleUser, req)
					if err != nil {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Failed to create user",
						}, err
					}
					isNewUser = true
				} else {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Database error",
					}, emailResult.Error
				}
			} else {
				// Link Google account to existing user
				err = s.linkGoogleAccount(&user, googleUser, req)
				if err != nil {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Failed to link Google account",
					}, err
				}
			}
		} else {
			return &dtos.OAuthResponse{
				Success: false,
				Error:   "Database error",
			}, result.Error
		}
	}

	// Generate JWT token using proper JWT utility
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Failed to generate token",
		}, err
	}

	refreshToken := fmt.Sprintf("refresh_token_%s_%d", user.ID.String(), time.Now().Unix())

	// Update last login
	s.db.Model(&user).Update("last_login", time.Now().Format(time.RFC3339))

	response := &dtos.OAuthResponse{
		Success:      true,
		Token:        token,
		RefreshToken: refreshToken,
	}

	response.User.ID = user.ID.String()
	response.User.Username = user.Username
	response.User.Email = user.Email
	response.User.Name = user.Name
	response.User.IsGuest = user.IsGuest
	response.User.Plan = user.Plan
	response.User.TransactionLimit = user.TransactionLimit
	response.User.TransactionCount = user.TransactionCount
	response.User.OAuthProvider = "google"
	response.User.GoogleID = *user.GoogleID
	response.User.Provider = "google"
	response.User.IsNewUser = isNewUser

	return response, nil
}

// Apple OAuth Authentication
func (s *OAuthService) AuthenticateWithApple(req *dtos.AppleOAuthRequest) (*dtos.OAuthResponse, error) {
	// Verify Apple Identity Token
	appleUser, err := s.verifyAppleIdentityToken(req.IdentityToken)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Invalid Apple identity token",
		}, err
	}

	// Check if user exists with Apple ID
	var user entities.User
	result := s.db.Where("apple_id = ?", appleUser.Sub).First(&user)

	isNewUser := false
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Check if user exists with same email
			if req.Email != "" {
				emailResult := s.db.Where("email = ?", req.Email).First(&user)
				if emailResult.Error != nil {
					if errors.Is(emailResult.Error, gorm.ErrRecordNotFound) {
						// Create new user
						user, err = s.createAppleUser(appleUser, req)
						if err != nil {
							return &dtos.OAuthResponse{
								Success: false,
								Error:   "Failed to create user",
							}, err
						}
						isNewUser = true
					} else {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Database error",
						}, emailResult.Error
					}
				} else {
					// Link Apple account to existing user
					err = s.linkAppleAccount(&user, appleUser, req)
					if err != nil {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Failed to link Apple account",
						}, err
					}
				}
			} else {
				// Create user without email (Apple private relay)
				user, err = s.createAppleUser(appleUser, req)
				if err != nil {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Failed to create user",
					}, err
				}
				isNewUser = true
			}
		} else {
			return &dtos.OAuthResponse{
				Success: false,
				Error:   "Database error",
			}, result.Error
		}
	}

	// Generate JWT token using proper JWT utility
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Failed to generate token",
		}, err
	}

	refreshToken := fmt.Sprintf("refresh_token_%s_%d", user.ID.String(), time.Now().Unix())

	// Update last login
	s.db.Model(&user).Update("last_login", time.Now().Format(time.RFC3339))

	response := &dtos.OAuthResponse{
		Success:      true,
		Token:        token,
		RefreshToken: refreshToken,
	}

	response.User.ID = user.ID.String()
	response.User.Username = user.Username
	response.User.Email = user.Email
	response.User.Name = user.Name
	response.User.IsGuest = user.IsGuest
	response.User.Plan = user.Plan
	response.User.TransactionLimit = user.TransactionLimit
	response.User.TransactionCount = user.TransactionCount
	response.User.OAuthProvider = "apple"
	if user.AppleID != nil {
		response.User.AppleID = *user.AppleID
	}
	response.User.Provider = "apple"
	response.User.IsNewUser = isNewUser

	return response, nil
}

// Verify Google ID Token
func (s *OAuthService) verifyGoogleIDToken(idToken string) (*dtos.GoogleUserInfo, error) {
	// Call Google's tokeninfo endpoint
	resp, err := http.Get(fmt.Sprintf("https://oauth2.googleapis.com/tokeninfo?id_token=%s", idToken))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New("invalid Google ID token")
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var googleUser dtos.GoogleUserInfo
	if err := json.Unmarshal(body, &googleUser); err != nil {
		return nil, err
	}

	return &googleUser, nil
}

// Verify Apple Identity Token (simplified - in production use Apple's public keys)
func (s *OAuthService) verifyAppleIdentityToken(identityToken string) (*dtos.AppleUserInfo, error) {
	// Parse JWT without verification for now (in production, verify with Apple's public keys)
	token, _, err := new(jwt.Parser).ParseUnverified(identityToken, jwt.MapClaims{})
	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid Apple identity token claims")
	}

	appleUser := &dtos.AppleUserInfo{
		Sub:   claims["sub"].(string),
		Email: "",
	}

	if email, exists := claims["email"]; exists {
		appleUser.Email = email.(string)
	}

	return appleUser, nil
}

// Create new user from Google OAuth
func (s *OAuthService) createGoogleUser(googleUser *dtos.GoogleUserInfo, req *dtos.GoogleOAuthRequest) (entities.User, error) {
	googleAuthData, _ := json.Marshal(map[string]interface{}{
		"id":               googleUser.ID,
		"email":            googleUser.Email,
		"verified_email":   googleUser.VerifiedEmail,
		"name":             googleUser.Name,
		"given_name":       googleUser.GivenName,
		"family_name":      googleUser.FamilyName,
		"picture":          googleUser.Picture,
		"locale":           googleUser.Locale,
		"access_token":     req.AccessToken,
		"authenticated_at": time.Now().Unix(),
	})

	googleAuthStr := string(googleAuthData)
	provider := "google"

	user := entities.User{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Username:         generateUsernameFromEmail(googleUser.Email),
		Email:            googleUser.Email,
		Name:             googleUser.Name,
		Status:           "active",
		GoogleID:         &googleUser.ID,
		GoogleAuth:       &googleAuthStr,
		OAuthProvider:    &provider,
		ProfileImageURL:  &googleUser.Picture,
		IsGuest:          false,
		Plan:             "free",
		TransactionLimit: 0, // Unlimited for registered users
		TransactionCount: 0,
	}

	if err := s.db.Debug().Create(&user).Error; err != nil {
		return entities.User{}, err
	}

	// Initialize default categories and accounts for the new user
	s.initializeUserDefaults(user.ID)

	return user, nil
}

// Initialize default categories and accounts for new OAuth user
func (s *OAuthService) initializeUserDefaults(userID uuid.UUID) {
	// Create default expense categories
	expenseCategories := []entities.Category{
		{Base: entities.Base{ID: uuid.New()}, Name: "Yemek", Type: "expense", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Ulaşım", Type: "expense", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Alışveriş", Type: "expense", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Faturalar", Type: "expense", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Eğlence", Type: "expense", UserID: userID},
	}

	// Create default income categories
	incomeCategories := []entities.Category{
		{Base: entities.Base{ID: uuid.New()}, Name: "Maaş", Type: "income", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Freelance", Type: "income", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Yatırım", Type: "income", UserID: userID},
	}

	// Create default accounts
	accounts := []entities.Account{
		{Base: entities.Base{ID: uuid.New()}, Name: "Nakit", Type: "nakit", Balance: 0, Currency: "TRY", UserID: userID},
		{Base: entities.Base{ID: uuid.New()}, Name: "Banka Hesabı", Type: "banka", Balance: 0, Currency: "TRY", UserID: userID},
	}

	// Insert categories and accounts
	s.db.Create(&expenseCategories)
	s.db.Create(&incomeCategories)
	s.db.Create(&accounts)
}

// Create new user from Apple OAuth
func (s *OAuthService) createAppleUser(appleUser *dtos.AppleUserInfo, req *dtos.AppleOAuthRequest) (entities.User, error) {
	appleAuthData, _ := json.Marshal(map[string]interface{}{
		"sub":                appleUser.Sub,
		"email":              req.Email,
		"user_identifier":    req.UserIdentifier,
		"identity_token":     req.IdentityToken,
		"authorization_code": req.AuthorizationCode,
		"full_name":          req.FullName,
		"authenticated_at":   time.Now().Unix(),
	})

	appleAuthStr := string(appleAuthData)
	provider := "apple"
	email := req.Email
	if email == "" {
		email = fmt.Sprintf("<EMAIL>", appleUser.Sub)
	}

	name := fmt.Sprintf("%s %s", req.FullName.GivenName, req.FullName.FamilyName)
	if strings.TrimSpace(name) == "" {
		name = "Apple User"
	}

	user := entities.User{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Username:      generateUsernameFromEmail(email),
		Email:         email,
		Name:          name,
		Status:        "active",
		AppleID:       &appleUser.Sub,
		AppleAuth:     &appleAuthStr,
		OAuthProvider: &provider,
	}

	if err := s.db.Create(&user).Error; err != nil {
		return entities.User{}, err
	}

	return user, nil
}

// Link Google account to existing user
func (s *OAuthService) linkGoogleAccount(user *entities.User, googleUser *dtos.GoogleUserInfo, req *dtos.GoogleOAuthRequest) error {
	googleAuthData, _ := json.Marshal(map[string]interface{}{
		"id":             googleUser.ID,
		"email":          googleUser.Email,
		"verified_email": googleUser.VerifiedEmail,
		"name":           googleUser.Name,
		"given_name":     googleUser.GivenName,
		"family_name":    googleUser.FamilyName,
		"picture":        googleUser.Picture,
		"locale":         googleUser.Locale,
		"access_token":   req.AccessToken,
		"linked_at":      time.Now().Unix(),
	})

	googleAuthStr := string(googleAuthData)

	updates := map[string]interface{}{
		"google_id":   googleUser.ID,
		"google_auth": googleAuthStr,
	}

	if user.ProfileImageURL == nil || *user.ProfileImageURL == "" {
		updates["profile_image_url"] = googleUser.Picture
	}

	return s.db.Model(user).Updates(updates).Error
}

// Link Apple account to existing user
func (s *OAuthService) linkAppleAccount(user *entities.User, appleUser *dtos.AppleUserInfo, req *dtos.AppleOAuthRequest) error {
	appleAuthData, _ := json.Marshal(map[string]interface{}{
		"sub":                appleUser.Sub,
		"email":              req.Email,
		"user_identifier":    req.UserIdentifier,
		"identity_token":     req.IdentityToken,
		"authorization_code": req.AuthorizationCode,
		"full_name":          req.FullName,
		"linked_at":          time.Now().Unix(),
	})

	appleAuthStr := string(appleAuthData)

	updates := map[string]interface{}{
		"apple_id":   appleUser.Sub,
		"apple_auth": appleAuthStr,
	}

	return s.db.Model(user).Updates(updates).Error
}

// Generate username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		username := strings.ReplaceAll(parts[0], ".", "_")
		return fmt.Sprintf("%s_%d", username, time.Now().Unix()%10000)
	}
	return fmt.Sprintf("user_%d", time.Now().Unix()%10000)
}
