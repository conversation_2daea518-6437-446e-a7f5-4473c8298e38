/**
 * Butce360 - Personal Finance Management App
 * Pure React Native Implementation
 */

import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { ThemeProvider } from './src/context/ThemeContext';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import MainNavigator from './src/navigation/MainNavigator';
import { useThemedColors } from './src/hooks/useThemedStyles';
// import { GoogleSignin } from '@react-native-google-signin/google-signin';

// Loading screen component
const LoadingScreen: React.FC = () => {
  const colors = useThemedColors();

  return (
    <View style={[loadingStyles.container, { backgroundColor: colors.background.primary }]}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text style={[loadingStyles.text, { color: colors.text.secondary }]}>
        Yükleniyor...
      </Text>
    </View>
  );
};

// App content component - auth-aware navigation
const AppContent: React.FC = () => {
  const { state } = useAuth();

  console.log('[App] Auth state:', {
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    isGuest: state.isGuest,
    user: state.user?.email
  });

  // Show loading screen while checking auth
  if (state.isLoading) {
    return <LoadingScreen />;
  }

  // Show main app if authenticated or in guest mode
  if (state.isAuthenticated || state.isGuest) {
    return <MainNavigator />;
  }

  // Show login screen if not authenticated
  return <MainNavigator />; // LoginScreen will be shown via MainNavigator
};

function App() {
  useEffect(() => {
    // Social login configuration will be added later
    console.log('App initialized');
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

// Loading screen styles
const loadingStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default App;
