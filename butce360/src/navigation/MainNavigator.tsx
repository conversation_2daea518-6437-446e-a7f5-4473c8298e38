import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemedColors } from '../hooks/useThemedStyles';
import { useAuth } from '../context/AuthContext';

// Import screens
import HomeScreen from '../screens/Home/HomeScreen';
import AddTransactionScreen from '../screens/Transactions/AddTransactionScreen';
import MenuScreen from '../screens/Menu/MenuScreen';
import BudgetScreen from '../screens/Budget/BudgetScreen';
import AddBudgetScreen from '../screens/Budget/AddBudgetScreen';
import CategoriesScreen from '../screens/Categories/CategoriesScreen';
import AddCategoryScreen from '../screens/Categories/AddCategoryScreen';
import AccountsScreen from '../screens/Accounts/AccountsScreen';
import AddAccountScreen from '../screens/Accounts/AddAccountScreen';
import ReportsScreen from '../screens/Reports/ReportsScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';
import ThemeScreen from '../screens/Theme/ThemeScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import EditProfileScreen from '../screens/Profile/EditProfileScreen';
import ProfileDetailsScreen from '../screens/Profile/ProfileDetailsScreen';
import ChangePasswordScreen from '../screens/Profile/ChangePasswordScreen';
import AboutScreen from '../screens/About/AboutScreen';
import HelpScreen from '../screens/Help/HelpScreen';
import TransactionsScreen from '../screens/Transactions/TransactionsScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import RegisterScreen from '../screens/Auth/RegisterScreen';
import BankStatementScreen from '../screens/BankStatement/BankStatementScreen';
import ExportScreen from '../screens/Export/ExportScreen';

// Import components
import TabBar from './BottomTabNavigator';
import NavigationHeader from '../components/common/NavigationHeader';

export interface NavigationProps {
  onNavigate: (screen: string) => void;
  onGoBack?: () => void;
}

const MainNavigator: React.FC = () => {
  const { state } = useAuth();
  const colors = useThemedColors();

  // Determine initial screen based on auth state
  const getInitialScreen = useCallback(() => {
    if (!state.isAuthenticated && !state.isGuest) {
      return 'login';
    }
    return 'home';
  }, [state.isAuthenticated, state.isGuest]);

  const [activeTab, setActiveTab] = useState('home');
  const [currentScreen, setCurrentScreen] = useState(getInitialScreen());
  const [navigationHistory, setNavigationHistory] = useState<string[]>([getInitialScreen()]);

  // Update screen when auth state changes
  useEffect(() => {
    const newScreen = getInitialScreen();
    console.log('[MainNavigator] Auth state changed - newScreen:', newScreen, 'currentScreen:', currentScreen);

    // Sadece authentication durumu değiştiğinde otomatik redirect yap
    // Manuel navigation'a müdahale etme
    if (!state.isAuthenticated && !state.isGuest && !['login', 'register'].includes(currentScreen)) {
      console.log('[MainNavigator] User not authenticated, redirecting to login');
      setCurrentScreen('login');
      setNavigationHistory(['login']);
    }
  }, [state.isAuthenticated, state.isGuest, currentScreen, getInitialScreen]);

  const handleNavigate = (screen: string) => {
    console.log('[MainNavigator] Navigating to screen:', screen);

    // Auth sayfalarına manuel navigation için özel durum
    if (screen === 'login' || screen === 'register') {
      setCurrentScreen(screen);
      setNavigationHistory([screen]); // Auth sayfaları için history'yi reset et
      return;
    }

    setCurrentScreen(screen);
    setNavigationHistory(prev => [...prev, screen]);

    // Ana tab sayfalarına dönüldüğünde activeTab'ı da güncelle
    const normalizedScreen = screen.toLowerCase();
    if (normalizedScreen === 'home') {
      setActiveTab('home');
      setCurrentScreen('home');
    } else if (normalizedScreen === 'menu') {
      setActiveTab('menu');
      setCurrentScreen('menu');
    }
  };

  const handleGoBack = () => {
    console.log('[MainNavigator] Going back');
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current screen
      const previousScreen = newHistory[newHistory.length - 1];

      setNavigationHistory(newHistory);
      setCurrentScreen(previousScreen);

      // If going back to a tab screen, update active tab
      if (['home', 'add', 'menu'].includes(previousScreen)) {
        setActiveTab(previousScreen);
      }
    } else {
      // If no history, go to menu
      setCurrentScreen('menu');
      setActiveTab('menu');
      setNavigationHistory(['menu']);
    }
  };

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
    setCurrentScreen(tab);
    setNavigationHistory([tab]); // Reset history when tab is pressed
  };

  const getScreenTitle = (screen: string): string => {
    const titles: { [key: string]: string } = {
      home: 'Ana Sayfa',
      add: 'İşlem Ekle',
      menu: 'Menü',
      budget: 'Bütçe',
      categories: 'Kategoriler',
      accounts: 'Hesaplar',
      reports: 'Raporlar',
      settings: 'Ayarlar',
      theme: 'Tema',
      profile: 'Profil',
      about: 'Hakkında',
      help: 'Yardım',
      transactions: 'İşlemler',
      addBudget: 'Yeni Bütçe',
      AddCategory: 'Kategori Ekle',
      AddAccount: 'Hesap Ekle',
    };
    return titles[screen] || screen;
  };

  const isTabScreen = (screen: string): boolean => {
    return ['home', 'add', 'menu'].includes(screen);
  };

  const shouldShowTabBar = (screen: string): boolean => {
    // Tab bar'ı göstermeyeceğimiz ekranlar (sadece auth ekranları)
    const noTabBarScreens = ['login', 'register'];
    return !noTabBarScreens.includes(screen);
  };

  const renderScreenWithHeader = (
    screenComponent: React.ReactNode,
    showHeader: boolean = true,
    headerProps?: {
      rightButtonText?: string;
      onRightButtonPress?: () => void;
    }
  ) => {
    if (!showHeader || isTabScreen(currentScreen)) {
      return screenComponent;
    }

    return (
      <>
        <NavigationHeader
          title={getScreenTitle(currentScreen)}
          showBackButton={navigationHistory.length > 1}
          onBackPress={handleGoBack}
          rightButtonText={headerProps?.rightButtonText}
          onRightButtonPress={headerProps?.onRightButtonPress}
        />
        {screenComponent}
      </>
    );
  };

  const renderScreen = () => {
    // Handle tab screens first (if current screen is a tab screen)
    if (isTabScreen(currentScreen)) {
      switch (currentScreen) {
        case 'home':
          return <HomeScreen />;
        case 'add':
          return <AddTransactionScreen onNavigate={handleNavigate} />;
        case 'menu':
          return <MenuScreen onNavigate={handleNavigate} />;
        default:
          return <HomeScreen />;
      }
    }

    // Handle non-tab screens
    switch (currentScreen) {
      case 'budget':
        return renderScreenWithHeader(
          <BudgetScreen onNavigate={handleNavigate} />,
          true,
          {
            rightButtonText: 'Ekle',
            onRightButtonPress: () => handleNavigate('addBudget')
          }
        );
      case 'categories':
        return renderScreenWithHeader(
          <CategoriesScreen onNavigate={handleNavigate} />,
          true,
          {
            rightButtonText: 'Ekle',
            onRightButtonPress: () => handleNavigate('AddCategory')
          }
        );
      case 'AddCategory':
        return renderScreenWithHeader(<AddCategoryScreen onNavigate={handleNavigate} />);
      case 'AddAccount':
        return renderScreenWithHeader(<AddAccountScreen onNavigate={handleNavigate} />);
      case 'accounts':
        return renderScreenWithHeader(
          <AccountsScreen onNavigate={handleNavigate} />,
          true,
          {
            rightButtonText: 'Ekle',
            onRightButtonPress: () => handleNavigate('AddAccount')
          }
        );
      case 'reports':
        return renderScreenWithHeader(<ReportsScreen />);
      case 'settings':
        return renderScreenWithHeader(<SettingsScreen onNavigate={handleNavigate} />);
      case 'theme':
        return renderScreenWithHeader(<ThemeScreen onNavigate={handleNavigate} />);
      case 'profile':
        return renderScreenWithHeader(<ProfileScreen onNavigate={handleNavigate} />);
      case 'ProfileDetails':
        return renderScreenWithHeader(<ProfileDetailsScreen onNavigate={handleNavigate} />);
      case 'about':
        return renderScreenWithHeader(<AboutScreen />);
      case 'help':
        return renderScreenWithHeader(<HelpScreen />);
      case 'transactions':
        return renderScreenWithHeader(<TransactionsScreen />);
      case 'login':
        return <LoginScreen onNavigate={handleNavigate} />;
      case 'register':
        return <RegisterScreen onNavigate={handleNavigate} />;
      case 'bankStatement':
        return <BankStatementScreen onNavigate={handleNavigate} />;
      case 'export':
        return <ExportScreen onNavigate={handleNavigate} />;
      case 'addBudget':
        return renderScreenWithHeader(<AddBudgetScreen onNavigate={handleNavigate} />);
      case 'EditProfile':
        return renderScreenWithHeader(<EditProfileScreen onNavigate={handleNavigate} />);
      case 'changePassword':
        return renderScreenWithHeader(<ChangePasswordScreen />);
      default:
        // Fallback to home screen
        return <HomeScreen />;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    screenContainer: {
      flex: 1,
    },
  });

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.screenContainer}>
        {renderScreen()}
      </View>
      {shouldShowTabBar(currentScreen) && (
        <TabBar activeTab={activeTab} onTabPress={handleTabPress} />
      )}
    </View>
  );
};

export default MainNavigator;
