import { apiClient } from './api';
import { authService } from './authService';
import { Account, CreateAccountRequest, UpdateAccountRequest } from '../types/models';
import { getActiveDefaultAccounts } from '../constants/defaultAccounts';
import { localAccountService } from './localAccountService';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class AccountService {
  private static instance: AccountService;

  private constructor() {}

  static getInstance(): AccountService {
    if (!AccountService.instance) {
      AccountService.instance = new AccountService();
    }
    return AccountService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Get all accounts
  async getAccounts(): Promise<Account[]> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();
      const isGuest = await AsyncStorage.getItem('isGuest');

      if (!token || isGuest === 'true') {
        // Guest mode: return local accounts with calculated balances
        console.log('[AccountService] Guest mode: returning local accounts');
        return await localAccountService.getAccounts();
      }

      // Authenticated user: fetch from API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<any>('/accounts', headers);

      console.log('[AccountService] Fetched accounts:', response.data);

      // Backend returns { data: [...], status: 200 } format
      const accounts = response.data?.data || response.data || [];

      // Transform backend format to frontend format
      const transformedAccounts = Array.isArray(accounts) ? accounts.map((account: any) => ({
        id: account.id,
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
        color: account.color || '#007AFF',
        icon: account.icon || '💳',
        isDefault: account.is_default || false,
        isActive: account.is_active !== false,
        createdAt: account.created_at ? new Date(account.created_at) : new Date(),
        updatedAt: account.updated_at ? new Date(account.updated_at) : new Date(),
      })) : [];

      return transformedAccounts;
    } catch (error) {
      console.error('[AccountService] Error fetching accounts:', error);
      // Fallback to default accounts if API fails
      console.log('[AccountService] Falling back to default accounts');
      return getActiveDefaultAccounts();
    }
  }

  // Get account by ID
  async getAccount(id: string): Promise<Account> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<any>(`/accounts/${id}`, headers);

      console.log('[AccountService] Fetched account:', response.data);

      // Backend returns { data: {...}, status: 200 } format
      const account = response.data?.data || response.data;

      return {
        id: account.id,
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
        color: account.color || '#007AFF',
        icon: account.icon || '💳',
        isDefault: account.is_default || false,
        createdAt: account.created_at ? new Date(account.created_at) : new Date(),
        updatedAt: account.updated_at ? new Date(account.updated_at) : new Date(),
      };
    } catch (error) {
      console.error('[AccountService] Error fetching account:', error);
      throw error;
    }
  }

  // Create new account
  async createAccount(accountData: CreateAccountRequest): Promise<Account> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<any>('/accounts', accountData, headers);

      console.log('[AccountService] Created account:', response.data);

      // Backend returns { data: {...}, status: 201 } format
      const account = response.data?.data || response.data;

      return {
        id: account.id,
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
        color: account.color || '#007AFF',
        icon: account.icon || '💳',
        isDefault: account.is_default || false,
        createdAt: account.created_at ? new Date(account.created_at) : new Date(),
        updatedAt: account.updated_at ? new Date(account.updated_at) : new Date(),
      };
    } catch (error) {
      console.error('[AccountService] Error creating account:', error);
      throw error;
    }
  }

  // Update account
  async updateAccount(id: string, accountData: UpdateAccountRequest): Promise<Account> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: update local account
        console.log('[AccountService] Guest mode: updating local account');
        const updatedAccount = await localAccountService.updateAccount(id, {
          name: accountData.name,
          type: accountData.type,
          balance: accountData.balance,
          currency: accountData.currency || 'TRY',
          color: accountData.color,
          icon: accountData.icon
        });
        return updatedAccount;
      }

      // Authenticated user: update via API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<any>(`/accounts/${id}`, accountData, headers);

      console.log('[AccountService] Updated account:', response.data);

      // Backend returns { data: {...}, status: 200 } format
      const account = response.data?.data || response.data;

      return {
        id: account.id,
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
        color: account.color || '#007AFF',
        icon: account.icon || '💳',
        isDefault: account.is_default || false,
        createdAt: account.created_at ? new Date(account.created_at) : new Date(),
        updatedAt: account.updated_at ? new Date(account.updated_at) : new Date(),
      };
    } catch (error) {
      console.error('[AccountService] Error updating account:', error);
      throw error;
    }
  }

  // Delete account
  async deleteAccount(id: string): Promise<void> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: delete local account
        console.log('[AccountService] Guest mode: deleting local account');
        await localAccountService.deleteAccount(id);
        return;
      }

      // Authenticated user: delete via API
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/accounts/${id}`, headers);

      console.log('[AccountService] Deleted account:', id);
    } catch (error) {
      console.error('[AccountService] Error deleting account:', error);
      throw error;
    }
  }

  // Get account balance
  async getAccountBalance(id: string): Promise<{ balance: number; currency: string }> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{ balance: number; currency: string }>(`/accounts/${id}/balance`, headers);

      console.log('[AccountService] Fetched account balance:', response.data);
      return response.data;
    } catch (error) {
      console.error('[AccountService] Error fetching account balance:', error);
      throw error;
    }
  }

  // Get account transactions
  async getAccountTransactions(
    id: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    transactions: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{
        transactions: any[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/accounts/${id}/transactions?page=${page}&limit=${limit}`, headers);

      console.log('[AccountService] Fetched account transactions:', response.data);
      return response.data;
    } catch (error) {
      console.error('[AccountService] Error fetching account transactions:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const accountService = AccountService.getInstance();
