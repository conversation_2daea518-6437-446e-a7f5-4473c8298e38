import { apiClient } from './api';
import { Category, CreateCategoryRequest, UpdateCategoryRequest } from '../types/models';
import { getDefaultCategoriesByType } from '../constants/defaultCategories';
import { localCategoryService } from './localCategoryService';
import { authService } from './authService';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class CategoryService {
  private static instance: CategoryService;

  private constructor() {}

  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  // Auth headers are handled by apiClient automatically

  // Check if user is authenticated (has valid token)
  private async isAuthenticated(): Promise<boolean> {
    try {
      const token = await authService.getStoredToken();
      return !!token;
    } catch {
      return false; // Default to not authenticated if error
    }
  }

  // Get local categories from storage
  private async getLocalCategories(): Promise<{ income: Category[]; expense: Category[] }> {
    try {
      const categories = await localCategoryService.getCategories();

      // Group categories by type
      const income = categories.filter(cat => cat.type === 'income');
      const expense = categories.filter(cat => cat.type === 'expense');

      console.log('[CategoryService] Local categories loaded:', { income: income.length, expense: expense.length });
      return { income, expense };
    } catch (error) {
      console.error('[CategoryService] Error reading local categories:', error);

      // Return default categories if error
      return {
        income: getDefaultCategoriesByType('income'),
        expense: getDefaultCategoriesByType('expense'),
      };
    }
  }

  // Get all categories grouped by type
  async getCategories(): Promise<{ income: Category[]; expense: Category[] }> {
    try {
      // Check if user is authenticated
      const isAuth = await this.isAuthenticated();

      if (!isAuth) {
        console.log('[CategoryService] Not authenticated - using local categories');
        return await this.getLocalCategories();
      }

      console.log('[CategoryService] Authenticated mode - fetching from API...');
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{ categories: Category[], status: number }>('/categories', { method: 'GET' }, token!);

      console.log('[CategoryService] API Response:', response.data);

      // Group categories by type
      const categories = response.data?.categories || [];

      // Ensure categories is an array
      if (!Array.isArray(categories)) {
        console.log('[CategoryService] API returned invalid categories data, using fallback');
        const income = getDefaultCategoriesByType('income');
        const expense = getDefaultCategoriesByType('expense');
        return { income, expense };
      }

      const income = categories.filter(cat => cat.type === 'income');
      const expense = categories.filter(cat => cat.type === 'expense');

      console.log('[CategoryService] Categories fetched:', {
        total: categories.length,
        income: income.length,
        expense: expense.length,
        sampleCategory: categories[0] // Debug: show first category structure
      });
      return { income, expense };
    } catch (error) {
      console.error('[CategoryService] Error fetching categories:', error);

      // Fallback to default categories
      console.log('[CategoryService] Using default categories as fallback');
      const income = getDefaultCategoriesByType('income');
      const expense = getDefaultCategoriesByType('expense');
      return { income, expense };
    }
  }

  // Get categories by type
  async getCategoriesByType(type: 'income' | 'expense'): Promise<Category[]> {
    try {
      const { income, expense } = await this.getCategories();
      return type === 'income' ? income : expense;
    } catch (error) {
      console.error('[CategoryService] Error fetching categories by type:', error);
      return getDefaultCategoriesByType(type);
    }
  }

  // Get category by ID
  async getCategoryById(id: string): Promise<Category> {
    try {
      console.log('[CategoryService] Fetching category:', id);
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{ category: Category }>(`/categories/${id}`, { method: 'GET' }, token!);

      console.log('[CategoryService] Category fetched:', response.data?.category.name);
      return response.data!.category;
    } catch (error) {
      console.error('[CategoryService] Error fetching category:', error);
      throw error;
    }
  }

  // Create new category
  async createCategory(categoryData: CreateCategoryRequest): Promise<Category> {
    try {
      console.log('[CategoryService] Creating category:', categoryData.name);

      // Check if user is authenticated
      const isAuth = await this.isAuthenticated();

      if (!isAuth) {
        // Guest mode: save to local storage using LocalCategoryService
        console.log('[CategoryService] Guest mode: creating category locally');

        const newCategory = await localCategoryService.addCategory({
          name: categoryData.name,
          type: categoryData.type,
          icon: categoryData.icon || (categoryData.type === 'income' ? '💰' : '💸'),
          color: categoryData.color || (categoryData.type === 'income' ? '#10B981' : '#EF4444'),
        });

        console.log('[CategoryService] Category created locally:', newCategory.name);
        return newCategory;
      }

      // Authenticated user: save to API
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{ category: Category }>('/categories', {
        method: 'POST',
        body: JSON.stringify(categoryData)
      }, token!);

      console.log('[CategoryService] Category created:', response.data?.category.name);
      return response.data!.category;
    } catch (error) {
      console.error('[CategoryService] Error creating category:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id: string, categoryData: UpdateCategoryRequest): Promise<Category> {
    try {
      console.log('[CategoryService] Updating category:', id);
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{ category: Category }>(`/categories/${id}`, {
        method: 'PUT',
        body: JSON.stringify(categoryData)
      }, token!);

      console.log('[CategoryService] Category updated:', response.data?.category.name);
      return response.data!.category;
    } catch (error) {
      console.error('[CategoryService] Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    try {
      console.log('[CategoryService] Deleting category:', id);
      const token = await authService.getStoredToken();
      await apiClient.authenticatedRequest(`/categories/${id}`, { method: 'DELETE' }, token!);

      console.log('[CategoryService] Category deleted successfully');
    } catch (error) {
      console.error('[CategoryService] Error deleting category:', error);
      throw error;
    }
  }

  // Get transactions for a specific category
  async getCategoryTransactions(id: string, page: number = 1, limit: number = 20): Promise<{
    transactions: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('[CategoryService] Fetching category transactions:', id);
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{
        transactions: any[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/categories/${id}/transactions?page=${page}&limit=${limit}`, { method: 'GET' }, token!);

      console.log('[CategoryService] Category transactions fetched:', response.data?.transactions?.length || 0);
      return response.data!;
    } catch (error) {
      console.error('[CategoryService] Error fetching category transactions:', error);
      throw error;
    }
  }

  // Sync local categories to API (called after login)
  async syncLocalCategoriesToAPI(): Promise<void> {
    try {
      console.log('[CategoryService] Syncing local categories to API...');

      const localCategories = await this.getLocalCategories();
      const allLocalCategories = [...localCategories.income, ...localCategories.expense];

      // Filter out default categories (only sync custom ones)
      const customCategories = allLocalCategories.filter(cat => !cat.isDefault);

      for (const category of customCategories) {
        try {
          const categoryData: CreateCategoryRequest = {
            name: category.name,
            type: category.type,
            color: category.color,
            icon: category.icon,
          };

          const token = await authService.getStoredToken();
          await apiClient.authenticatedRequest('/categories', {
            method: 'POST',
            body: JSON.stringify(categoryData)
          }, token!);
          console.log('[CategoryService] Synced category:', category.name);
        } catch (error) {
          console.error('[CategoryService] Failed to sync category:', category.name, error);
        }
      }

      // Clear local categories after successful sync
      await AsyncStorage.removeItem('local_categories');
      console.log('[CategoryService] Local categories synced and cleared');
    } catch (error) {
      console.error('[CategoryService] Error syncing local categories:', error);
    }
  }
}

// Export singleton instance
export const categoryService = CategoryService.getInstance();
