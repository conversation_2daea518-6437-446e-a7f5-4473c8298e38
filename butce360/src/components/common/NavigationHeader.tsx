import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

interface NavigationHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  rightButtonText?: string;
  onRightButtonPress?: () => void;
  backgroundColor?: string;
}

const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  showBackButton = true,
  onBackPress,
  rightComponent,
  rightButtonText,
  onRightButtonPress,
  backgroundColor,
}) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: insets.top + 8,
          backgroundColor: backgroundColor || colors.background.primary,
          borderBottomColor: colors.border.primary,
        },
      ]}
    >
      <View style={styles.content}>
        {showBackButton && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBackPress}
            activeOpacity={0.7}
          >
            <Text style={[styles.backIcon, { color: colors.text.primary }]}>
              ←
            </Text>
          </TouchableOpacity>
        )}
        
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.title,
              { color: colors.text.primary },
              !showBackButton && styles.titleCentered,
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {title}
          </Text>
        </View>

        <View style={styles.rightContainer}>
          {rightComponent}
          {rightButtonText && onRightButtonPress && (
            <TouchableOpacity
              style={[styles.rightButton, { backgroundColor: colors.primary[500] }]}
              onPress={onRightButtonPress}
              activeOpacity={0.8}
            >
              <Text style={[styles.rightButtonText, { color: colors.background.secondary }]}>
                {rightButtonText}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 0.5,
    paddingBottom: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    minHeight: 44,
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 8,
    marginLeft: -4,
    minWidth: 44,
    minHeight: 44,
  },
  backIcon: {
    fontSize: 28,
    fontWeight: '300',
    lineHeight: 32,
  },
  backIconWhite: {
    fontSize: 28,
    fontWeight: '300',
    lineHeight: 32,
    color: '#ffffff',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  title: {
    ...typography.styles.navigationTitle,
    textAlign: 'center',
  },
  titleCentered: {
    textAlign: 'center',
  },
  rightContainer: {
    minWidth: 60,
    alignItems: 'flex-end',
  },
  rightButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  rightButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default NavigationHeader;
