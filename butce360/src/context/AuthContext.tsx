import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { authService } from '../services/authService';
import { storage, STORAGE_KEYS } from '../services/storage';
import { User, LoginRequest, RegisterRequest, GuestToRegisteredRequest, TransactionLimitResponse } from '../types/models';

// Auth state interface
interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  isGuest: boolean;
  user: User | null;
  token: string | null;
  error: string | null;
}

// Auth actions
type AuthAction =
  | { type: 'AUTH_LOADING' }
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string; isGuest?: boolean } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_GUEST_MODE'; payload: boolean }
  | { type: 'AUTH_CLEAR_ERROR' }
  | { type: 'AUTH_UPDATE_USER'; payload: User };

// Initial state
const initialState: AuthState = {
  isLoading: false,
  isAuthenticated: false,
  isGuest: true, // Default olarak misafir
  user: null,
  token: null,
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_LOADING':
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        isGuest: action.payload.isGuest ?? false,
        user: action.payload.user,
        token: action.payload.token,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: true, // Hata durumunda misafir moda dön
        user: null,
        token: null,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: false, // Logout durumunda tamamen çıkış yap
        user: null,
        token: null,
        error: null,
      };

    case 'AUTH_GUEST_MODE':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: action.payload,
        user: null,
        token: null,
        error: null,
      };

    case 'AUTH_CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'AUTH_UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };

    default:
      return state;
  }
};

// Auth context interface
interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  googleSignIn: (user: any, token: string) => Promise<void>;
  appleSignIn: (user: any, token: string) => Promise<void>;
  logout: () => Promise<void>;
  enableGuestMode: () => Promise<void>;
  disableGuestMode: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;

  // Guest user methods
  createGuestUser: (deviceId: string) => Promise<void>;
  convertGuestToRegistered: (userData: GuestToRegisteredRequest) => Promise<void>;
  getTransactionLimitStatus: () => Promise<TransactionLimitResponse>;

  // Data synchronization
  syncGuestDataToUser: () => Promise<void>;
  clearLocalStorage: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on app start
  const checkAuthStatus = useCallback(async () => {
    try {
      // Prevent multiple simultaneous calls
      if (state.isLoading) {
        return;
      }

      dispatch({ type: 'AUTH_LOADING' });

      // Check if user is authenticated (token ve user var mı?)
      const isAuthenticated = await authService.isAuthenticated();
      if (isAuthenticated) {
        const user = await authService.getStoredUser();
        const token = await authService.getStoredToken();

        if (user && token) {
          console.log('[AuthContext] User authenticated from storage');
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: { user, token }
          });
          return;
        }
      }

      // Token yoksa veya geçersizse misafir modda kal
      console.log('[AuthContext] No valid auth found, enabling guest mode');
      await enableGuestMode();
    } catch (error) {
      console.error('[Auth] Error checking auth status:', error);
      await enableGuestMode();
    }
  }, [state.isLoading]);

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      const wasGuest = state.isGuest;
      dispatch({ type: 'AUTH_LOADING' });

      const response = await authService.login(credentials);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });

      // If user was in guest mode, sync local data to backend
      if (wasGuest) {
        console.log('[AuthContext] User was in guest mode, syncing data...');
        try {
          await syncGuestDataToUser();
        } catch (syncError) {
          console.error('[AuthContext] Data sync failed, but login was successful:', syncError);
          // Don't throw error here, login was successful
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Login failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Register function
  const register = async (userData: RegisterRequest) => {
    try {
      const wasGuest = state.isGuest;
      dispatch({ type: 'AUTH_LOADING' });

      const response = await authService.register(userData);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });

      // If user was in guest mode, sync local data to backend
      if (wasGuest) {
        console.log('[AuthContext] User was in guest mode, syncing data after registration...');
        try {
          await syncGuestDataToUser();
        } catch (syncError) {
          console.error('[AuthContext] Data sync failed, but registration was successful:', syncError);
          // Don't throw error here, registration was successful
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Registration failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Google Sign-In function
  const googleSignIn = async (user: any, token: string) => {
    try {
      const wasGuest = state.isGuest;
      dispatch({ type: 'AUTH_LOADING' });

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: user,
          token: token
        }
      });

      // If user was in guest mode, sync local data to backend
      if (wasGuest) {
        console.log('[AuthContext] User was in guest mode, syncing data after Google sign-in...');
        try {
          await syncGuestDataToUser();
        } catch (syncError) {
          console.error('[AuthContext] Data sync failed, but Google sign-in was successful:', syncError);
          // Don't throw error here, sign-in was successful
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Google sign-in failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Apple Sign-In function
  const appleSignIn = async (user: any, token: string) => {
    try {
      const wasGuest = state.isGuest;
      dispatch({ type: 'AUTH_LOADING' });

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: user,
          token: token
        }
      });

      // If user was in guest mode, sync local data to backend
      if (wasGuest) {
        console.log('[AuthContext] User was in guest mode, syncing data after Apple sign-in...');
        try {
          await syncGuestDataToUser();
        } catch (syncError) {
          console.error('[AuthContext] Data sync failed, but Apple sign-in was successful:', syncError);
          // Don't throw error here, sign-in was successful
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Apple sign-in failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authService.logout();

      // Clear local storage when logging out
      await clearLocalStorage();

      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      // Force logout even if server request fails
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  // Enable guest mode (zaten default misafir, sadece state güncelle)
  const enableGuestMode = async () => {
    try {
      console.log('[AuthContext] Enabling guest mode...');
      await authService.enableGuestMode();
      dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
      console.log('[AuthContext] Guest mode enabled successfully');
    } catch (error) {
      console.error('[Auth] Error enabling guest mode:', error);
      // Hata olsa bile misafir modda kal
      dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
    }
  };

  // Disable guest mode (logout yap, misafir moddan çık)
  const disableGuestMode = async () => {
    try {
      await authService.disableGuestMode();
      // disableGuestMode logout anlamına gelir, ama yine misafir modda kalır
      dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
    } catch (error) {
      console.error('[Auth] Error disabling guest mode:', error);
      // Hata olsa bile misafir modda kal
      dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
    }
  };

  // Update profile
  const updateProfile = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData);
      dispatch({ type: 'AUTH_UPDATE_USER', payload: updatedUser });
    } catch (error) {
      console.error('[Auth] Error updating profile:', error);
      throw error;
    }
  };

  // Change password
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      await authService.changePassword(currentPassword, newPassword);
    } catch (error) {
      console.error('[Auth] Error changing password:', error);
      throw error;
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'AUTH_CLEAR_ERROR' });
  };

  // Guest user methods
  const createGuestUser = async (deviceId: string) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authService.createGuestUser(deviceId);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          isGuest: true,
        },
      });
    } catch (error: any) {
      dispatch({
        type: 'AUTH_ERROR',
        payload: error.message || 'Failed to create guest user',
      });
      throw error;
    }
  };

  const convertGuestToRegistered = async (userData: GuestToRegisteredRequest) => {
    try {
      dispatch({ type: 'AUTH_START' });

      const response = await authService.convertGuestToRegistered(userData);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          isGuest: false,
        },
      });
    } catch (error: any) {
      dispatch({
        type: 'AUTH_ERROR',
        payload: error.message || 'Failed to convert guest user',
      });
      throw error;
    }
  };

  const getTransactionLimitStatus = async (): Promise<TransactionLimitResponse> => {
    try {
      return await authService.getTransactionLimitStatus();
    } catch (error: any) {
      throw error;
    }
  };

  // Clear local storage utility function
  const clearLocalStorage = async () => {
    try {
      console.log('[AuthContext] Clearing all local storage data...');
      await storage.removeItem(STORAGE_KEYS.LOCAL_TRANSACTIONS);
      await storage.removeItem(STORAGE_KEYS.LOCAL_ACCOUNTS);
      await storage.removeItem(STORAGE_KEYS.LOCAL_CATEGORIES);
      await storage.removeItem(STORAGE_KEYS.LOCAL_BUDGETS);
      console.log('[AuthContext] Local storage cleared successfully');
    } catch (error) {
      console.error('[AuthContext] Error clearing local storage:', error);
    }
  };

  // Sync guest data to authenticated user
  const syncGuestDataToUser = async (): Promise<void> => {
    try {
      console.log('[AuthContext] Starting guest data synchronization...');

      // Import services here to avoid circular dependencies
      const { transactionService } = await import('../services/transactionService');
      const { accountService } = await import('../services/accountService');
      const { categoryService } = await import('../services/categoryService');
      const { budgetService } = await import('../services/budgetService');
      const { storage: storageService } = await import('../services/storage');

      // Get all local data using storage keys
      const localTransactions = (await storageService.getObject(STORAGE_KEYS.LOCAL_TRANSACTIONS) || []) as any[];
      const localAccounts = (await storageService.getObject(STORAGE_KEYS.LOCAL_ACCOUNTS) || []) as any[];
      const localCategories = (await storageService.getObject(STORAGE_KEYS.LOCAL_CATEGORIES) || []) as any[];
      const localBudgets = (await storageService.getObject(STORAGE_KEYS.LOCAL_BUDGETS) || []) as any[];

      console.log('[AuthContext] Local data counts:', {
        transactions: localTransactions.length,
        accounts: localAccounts.length,
        categories: localCategories.length,
        budgets: localBudgets.length
      });

      // Sync data to backend (only if user is authenticated)
      if (state.isAuthenticated && state.token) {
        let syncErrors = [];

        // Sync accounts first
        console.log('[AuthContext] Syncing accounts:', localAccounts.length);
        for (const account of localAccounts) {
          try {
            await accountService.createAccount({
              name: account.name,
              type: account.type,
              balance: account.balance,
              currency: account.currency
            });
            console.log('[AuthContext] Account synced successfully:', account.name);
          } catch (error) {
            console.error('[AuthContext] Error syncing account:', account.name, error);
            syncErrors.push(`Account: ${account.name}`);
          }
        }

        // Sync categories
        console.log('[AuthContext] Syncing categories:', localCategories.length);
        for (const category of localCategories) {
          try {
            console.log('[AuthContext] Syncing category:', category.name, category.type);
            const syncedCategory = await categoryService.createCategory({
              name: category.name,
              type: category.type,
              color: category.color,
              icon: category.icon
            });
            console.log('[AuthContext] Category synced successfully:', syncedCategory.name, 'ID:', syncedCategory.id);
          } catch (error) {
            console.error('[AuthContext] Error syncing category:', category.name, error);
          }
        }

        // Sync budgets
        console.log('[AuthContext] Syncing budgets:', localBudgets.length);
        for (const budget of localBudgets) {
          try {
            await budgetService.createBudget({
              name: budget.name,
              amount: budget.amount,
              category_id: budget.categoryId,
              period: budget.period,
              color: budget.color
            });
            console.log('[AuthContext] Budget synced successfully:', budget.name);
          } catch (error) {
            console.error('[AuthContext] Error syncing budget:', budget.name, error);
            syncErrors.push(`Budget: ${budget.name}`);
          }
        }

        // Sync transactions last (after accounts and categories are created)
        console.log('[AuthContext] Syncing transactions:', localTransactions.length);
        for (const transaction of localTransactions) {
          try {
            await transactionService.createTransaction({
              amount: transaction.amount,
              description: transaction.description,
              categoryId: transaction.categoryId,
              accountId: transaction.accountId,
              type: transaction.type,
              date: transaction.date
            });
            console.log('[AuthContext] Transaction synced successfully:', transaction.description);
          } catch (error) {
            console.error('[AuthContext] Error syncing transaction:', transaction.description, error);
            syncErrors.push(`Transaction: ${transaction.description}`);
          }
        }

        if (syncErrors.length > 0) {
          console.warn('[AuthContext] Some items failed to sync:', syncErrors);
        }

        console.log('[AuthContext] Data synchronization completed successfully');

        // Clear local data after successful sync
        await clearLocalStorage();
      }
    } catch (error) {
      console.error('[AuthContext] Error during data synchronization:', error);
      throw error;
    }
  };

  // Auth status will be checked manually from App.tsx
  // to control the timing and initial navigation

  const contextValue: AuthContextType = {
    state,
    login,
    register,
    googleSignIn,
    appleSignIn,
    logout,
    enableGuestMode,
    disableGuestMode,
    updateProfile,
    changePassword,
    clearError,
    checkAuthStatus,
    createGuestUser,
    convertGuestToRegistered,
    getTransactionLimitStatus,
    syncGuestDataToUser,
    clearLocalStorage,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};