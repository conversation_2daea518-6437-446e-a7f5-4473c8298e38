import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

const ACCOUNT_TYPES = [
  {
    id: 'checking',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'business-outline',
    color: '#3b82f6',
  },
  {
    id: 'savings',
    name: '<PERSON><PERSON><PERSON>',
    icon: 'cash-outline',
    color: '#10b981',
  },
  { id: 'credit', name: '<PERSON><PERSON><PERSON>', icon: 'card-outline', color: '#ef4444' },
  { id: 'cash', name: '<PERSON><PERSON><PERSON>', icon: 'wallet-outline', color: '#f59e0b' },
  {
    id: 'investment',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: 'trending-up-outline',
    color: '#8b5cf6',
  },
  { id: 'other', name: '<PERSON><PERSON><PERSON>', icon: 'pricetag-outline', color: '#64748b' },
];

const CURRENCIES = [
  { code: 'TRY', symbol: '₺', name: 'Türk Lirası' },
  { code: 'USD', symbol: '$', name: 'Amerikan Doları' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'İngiliz Sterlini' },
];

interface AddAccountScreenProps {
  onNavigate?: (screen: string) => void;
}

const AddAccountScreen: React.FC<AddAccountScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);

  const [formData, setFormData] = useState({
    name: '',
    type: 'checking',
    balance: '',
    currency: 'TRY',
    description: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Hesap adı gerekli';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Hesap adı en az 2 karakter olmalı';
    }

    if (!formData.balance.trim()) {
      newErrors.balance = 'Başlangıç bakiyesi gerekli';
    } else if (isNaN(Number(formData.balance))) {
      newErrors.balance = 'Geçerli bir sayı girin';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // TODO: API call to create account
      await new Promise<void>(resolve => setTimeout(resolve, 1000)); // Mock API call

      Alert.alert('Başarılı', 'Hesap başarıyla oluşturuldu', [
        { text: 'Tamam', onPress: () => onNavigate?.('accounts') },
      ]);
    } catch (error) {
      Alert.alert('Hata', 'Hesap oluşturulurken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const selectedAccountType = ACCOUNT_TYPES.find(
    type => type.id === formData.type,
  );
  const selectedCurrency = CURRENCIES.find(
    curr => curr.code === formData.currency,
  );

  return (
    <View
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.content}
          contentContainerStyle={[
            styles.scrollContent,
            {
              paddingBottom: spacing.xl,
            },
          ]}
          showsVerticalScrollIndicator={false}
        >
          {/* Account Preview */}
          <View style={styles.previewSection}>
            <View
              style={[
                styles.accountPreview,
                { backgroundColor: selectedAccountType?.color },
              ]}
            >
              <Ionicons
                name={selectedAccountType?.icon || 'business-outline'}
                size={32}
                color="#ffffff"
              />
            </View>
            <Text style={styles.previewName}>
              {formData.name || 'Hesap Adı'}
            </Text>
            <Text style={styles.previewBalance}>
              {selectedCurrency?.symbol}
              {formData.balance || '0.00'}
            </Text>
            <Text style={styles.previewType}>{selectedAccountType?.name}</Text>
          </View>

          <View style={styles.formSection}>
            {/* Account Name */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Hesap Adı *</Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={formData.name}
                onChangeText={value => handleInputChange('name', value)}
                placeholder="Hesap adını girin"
                placeholderTextColor={colors.text.secondary}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
            </View>

            {/* Account Type */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Hesap Türü</Text>
              <View style={styles.typeGrid}>
                {ACCOUNT_TYPES.map(type => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.typeButton,
                      formData.type === type.id && styles.typeButtonActive,
                    ]}
                    onPress={() => handleInputChange('type', type.id)}
                  >
                    <Ionicons
                      name={type.icon}
                      size={24}
                      color={
                        formData.type === type.id
                          ? colors.text.inverse
                          : colors.text.secondary
                      }
                    />
                    <Text
                      style={[
                        styles.typeButtonText,
                        formData.type === type.id &&
                          styles.typeButtonTextActive,
                      ]}
                    >
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Balance */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Başlangıç Bakiyesi *</Text>
              <View style={styles.balanceInputContainer}>
                <Text style={styles.currencySymbol}>
                  {selectedCurrency?.symbol}
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    styles.balanceInput,
                    errors.balance && styles.inputError,
                  ]}
                  value={formData.balance}
                  onChangeText={value => handleInputChange('balance', value)}
                  placeholder="0.00"
                  placeholderTextColor={colors.text.secondary}
                  keyboardType="numeric"
                />
              </View>
              {errors.balance && (
                <Text style={styles.errorText}>{errors.balance}</Text>
              )}
            </View>

            {/* Currency */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Para Birimi</Text>
              <View style={styles.currencyGrid}>
                {CURRENCIES.map(currency => (
                  <TouchableOpacity
                    key={currency.code}
                    style={[
                      styles.currencyButton,
                      formData.currency === currency.code &&
                        styles.currencyButtonActive,
                    ]}
                    onPress={() => handleInputChange('currency', currency.code)}
                  >
                    <Text style={[
                      styles.currencySymbolButton,
                      {
                        color: formData.currency === currency.code
                          ? colors.text.inverse
                          : colors.text.primary
                      }
                    ]}>
                      {currency.symbol}
                    </Text>
                    <Text
                      style={[
                        styles.currencyButtonText,
                        formData.currency === currency.code &&
                          styles.currencyButtonTextActive,
                      ]}
                    >
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Açıklama (Opsiyonel)</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.description}
                onChangeText={value => handleInputChange('description', value)}
                placeholder="Hesap hakkında kısa bir açıklama yazın"
                placeholderTextColor={colors.text.secondary}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
            <TouchableOpacity
              onPress={handleSave}
              style={[
                styles.saveButton,
                { backgroundColor: colors.primary[500] },
                isLoading && styles.saveButtonDisabled,
              ]}
              disabled={isLoading}
            >
              <Text
                style={[
                  styles.saveButtonText,
                  { color: colors.background.secondary },
                ]}
              >
                {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: spacing.screenPadding,
      paddingTop: spacing.sm,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.screenPadding,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
    },
    backButton: {
      width: 40,
      height: 40,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 20,
    },
    headerTitle: {
      ...typography.styles.title2,
      flex: 1,
      textAlign: 'center',
    },
    saveButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.buttonPaddingVertical,
      borderRadius: spacing.md,
      alignItems: 'center' as const,
      marginTop: spacing.lg,
      minHeight: 48,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
    ...typography.styles.button,
    color: colors.background.secondary,
  },
    content: {
      flex: 1,
    },
    previewSection: {
      alignItems: 'center',
      paddingVertical: spacing.xl,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    },
    accountPreview: {
      width: 80,
      height: 80,
      borderRadius: 40,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.md,
    },
    accountIcon: {
      fontSize: 32,
    },
    previewName: {
      ...typography.styles.h4,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    previewBalance: {
      ...typography.styles.h2,
      color: colors.primary[500],
      fontWeight: 'bold',
      marginBottom: spacing.xs,
    },
    previewType: {
      ...typography.styles.caption,
      color: colors.text.secondary,
    },
    formSection: {
      paddingTop: spacing.md,
    },
    inputGroup: {
      marginBottom: spacing.lg,
    },
    inputLabel: {
      ...typography.styles.body1,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    input: {
      borderWidth: 1,
      borderColor: colors.border.primary,
      borderRadius: spacing.md,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      fontSize: 16,
      color: colors.text.primary,
      backgroundColor: colors.background.secondary,
      minHeight: 48,
    },
    inputError: {
      borderColor: colors.error[500],
    },
    textArea: {
      height: 80,
      paddingTop: spacing.md,
    },
    errorText: {
      ...typography.styles.caption,
      color: colors.error[500],
      marginTop: spacing.xs,
    },
    typeGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.sm,
    },
    typeButton: {
      flex: 1,
      minWidth: '48%',
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.sm,
      borderRadius: spacing.md,
      borderWidth: 1,
      borderColor: colors.border.primary,
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
    },
    typeButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    typeIcon: {
      fontSize: 24,
      marginBottom: spacing.xs,
    },
    typeButtonText: {
      ...typography.styles.caption,
      color: colors.text.primary,
      textAlign: 'center',
    },
    typeButtonTextActive: {
      color: colors.text.inverse,
    },
    balanceInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    currencySymbol: {
      ...typography.styles.h4,
      color: colors.text.primary,
      marginRight: spacing.sm,
    },
    balanceInput: {
      flex: 1,
    },
    currencyGrid: {
      flexDirection: 'row',
      gap: spacing.sm,
    },
    currencyButton: {
      flex: 1,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.sm,
      borderRadius: spacing.md,
      borderWidth: 1,
      borderColor: colors.border.primary,
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
    },
    currencyButtonActive: {
      backgroundColor: colors.primary[500],
      borderColor: colors.primary[500],
    },
    currencySymbolButton: {
      fontSize: 20,
      marginBottom: spacing.xs,
    },
    currencyButtonText: {
      ...typography.styles.caption,
      color: colors.text.primary,
    },
    currencyButtonTextActive: {
      color: colors.text.inverse,
    },
  });

export default AddAccountScreen;
