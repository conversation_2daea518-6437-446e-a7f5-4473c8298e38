import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { useAuth } from '../../context/AuthContext';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { authService } from '../../services/authService';

interface ProfileDetailsScreenProps {
  onNavigate?: (screen: string) => void;
}

interface UserProfile {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  oauthProvider?: string;
}

const ProfileDetailsScreen: React.FC<ProfileDetailsScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const { state: authState } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const token = await authService.getStoredToken();
      
      if (!token) {
        Alert.alert('Hata', 'Oturum bilgisi bulunamadı');
        onNavigate?.('Profile');
        return;
      }

      // /me endpoint'ine istek at
      const response = await fetch('http://localhost:8008/api/v1/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ProfileDetails] User profile data:', data);
      
      // API response format'ına göre data'yı parse et
      const userData = data.data || data;
      setProfile(userData);
    } catch (error) {
      console.error('[ProfileDetails] Error fetching profile:', error);
      Alert.alert('Hata', 'Profil bilgileri yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const styles = createStyles(colors);

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor="#000" barStyle="light-content" />
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => onNavigate?.('Profile')}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profil Bilgileri</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0ea5e9" />
          <Text style={styles.loadingText}>Profil bilgileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#000" barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => onNavigate?.('Profile')}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profil Bilgileri</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {profile && (
          <View style={styles.profileCard}>
            {/* Profile Icon */}
            <View style={styles.profileIconContainer}>
              <Ionicons name="person-circle" size={80} color="#0ea5e9" />
            </View>

            {/* Profile Info */}
            <View style={styles.profileInfo}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Ad Soyad</Text>
                <Text style={styles.infoValue}>{profile.name || 'Belirtilmemiş'}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>E-posta</Text>
                <Text style={styles.infoValue}>{profile.email}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Kullanıcı ID</Text>
                <Text style={styles.infoValue}>{profile.id}</Text>
              </View>

              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Kayıt Tarihi</Text>
                <Text style={styles.infoValue}>
                  {profile.createdAt ? formatDate(profile.createdAt) : 'Belirtilmemiş'}
                </Text>
              </View>

              {profile.oauthProvider && (
                <View style={styles.infoItem}>
                  <Text style={styles.infoLabel}>Giriş Yöntemi</Text>
                  <Text style={styles.infoValue}>
                    {profile.oauthProvider === 'google' ? 'Google' : 
                     profile.oauthProvider === 'apple' ? 'Apple' : 
                     profile.oauthProvider}
                  </Text>
                </View>
              )}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  console.log('[ProfileDetailsScreen] Edit profile clicked');
                  console.log('[ProfileDetailsScreen] onNavigate function:', onNavigate);
                  onNavigate?.('EditProfile');
                }}
              >
                <Ionicons name="create-outline" size={20} color="#0ea5e9" />
                <Text style={styles.editButtonText}>Profili Düzenle</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.refreshButton}
                onPress={fetchUserProfile}
              >
                <Ionicons name="refresh-outline" size={20} color="#6b7280" />
                <Text style={styles.refreshButtonText}>Yenile</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: '#333',
      backgroundColor: '#000',
    },
    backButton: {
      padding: spacing.xs,
      marginRight: spacing.sm,
    },
    headerTitle: {
      fontSize: 22,
      fontWeight: '600',
      color: '#fff',
      flex: 1,
    },
    content: {
      flex: 1,
      padding: spacing.md,
      backgroundColor: '#000',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      fontWeight: '400',
      color: '#9ca3af',
      marginTop: spacing.sm,
    },
    profileCard: {
      backgroundColor: '#1a1a1a',
      borderRadius: 12,
      padding: spacing.lg,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.3,
      shadowRadius: 3.84,
      elevation: 5,
    },
    profileIconContainer: {
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    profileInfo: {
      marginBottom: spacing.lg,
    },
    infoItem: {
      marginBottom: spacing.md,
      paddingBottom: spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: '#333',
    },
    infoLabel: {
      fontSize: 12,
      fontWeight: '400',
      color: '#9ca3af',
      marginBottom: spacing.xs,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    infoValue: {
      fontSize: 16,
      fontWeight: '500',
      color: '#fff',
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: spacing.sm,
    },
    editButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#0ea5e9',
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: 8,
      gap: spacing.xs,
    },
    editButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#fff',
    },
    refreshButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#333',
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: 8,
      gap: spacing.xs,
    },
    refreshButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#fff',
    },
  });

export default ProfileDetailsScreen;
