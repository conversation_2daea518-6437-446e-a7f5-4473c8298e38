import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';


import { useThemedColors } from '../../hooks/useThemedStyles';
import { categoryService } from '../../services/categoryService';
import { Category } from '../../types/models';
import { typography } from '../../theme/typography';
import NavigationHeader from '../../components/common/NavigationHeader';

interface CategoryDetailScreenProps {
  onNavigate?: (screen: string) => void;
  onGoBack?: () => void;
  categoryId?: string;
  categoryName?: string;
}

const CategoryDetailScreen: React.FC<CategoryDetailScreenProps> = ({ 
  onNavigate, 
  onGoBack,
  categoryId,
  categoryName 
}) => {
  const colors = useThemedColors();
  const [category, setCategory] = useState<Category | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch category details and transactions
  const fetchCategoryData = useCallback(async () => {
    if (!categoryId) return;
    
    try {
      setLoading(true);
      
      // Fetch category details
      const categoryData = await categoryService.getCategoryById(categoryId);
      setCategory(categoryData);
      
      // Fetch category transactions
      const transactionsData = await categoryService.getCategoryTransactions(categoryId);
      setTransactions(transactionsData.transactions || []);
      
    } catch (error) {
      console.error('[CategoryDetailScreen] Error fetching data:', error);
      Alert.alert('Hata', 'Kategori bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [categoryId]);

  useEffect(() => {
    fetchCategoryData();
  }, [categoryId, fetchCategoryData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchCategoryData();
    setRefreshing(false);
  };

  const handleEditCategory = () => {
    onNavigate && onNavigate(`EditCategory?id=${categoryId}`);
  };

  const handleDeleteCategory = async () => {
    if (!category || !categoryId) return;
    
    if (category.isDefault) {
      Alert.alert(
        'Varsayılan Kategori',
        'Varsayılan kategoriler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Kategori Sil',
      `"${category.name}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await categoryService.deleteCategory(categoryId);
              Alert.alert('Başarılı', 'Kategori başarıyla silindi.', [
                { text: 'Tamam', onPress: () => onGoBack && onGoBack() }
              ]);
            } catch (error) {
              console.error('[CategoryDetailScreen] Error deleting category:', error);
              Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  const renderTransactionItem = (transaction: any) => (
    <View key={transaction.id} style={[styles.transactionItem, { backgroundColor: colors.background.secondary }]}>
      <View style={styles.transactionLeft}>
        <Text style={[styles.transactionDescription, { color: colors.text.primary }]}>
          {transaction.description || 'İşlem'}
        </Text>
        <Text style={[styles.transactionDate, { color: colors.text.secondary }]}>
          {new Date(transaction.date).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      <Text style={[
        styles.transactionAmount,
        { color: transaction.type === 'income' ? colors.success[500] : colors.error[500] }
      ]}>
        {transaction.type === 'income' ? '+' : '-'}₺{Math.abs(transaction.amount).toFixed(2)}
      </Text>
    </View>
  );

  const renderEmptyTransactions = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>📊</Text>
      <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
        Henüz işlem yok
      </Text>
      <Text style={[styles.emptyStateText, { color: colors.text.secondary }]}>
        Bu kategoride henüz hiç işlem bulunmuyor.
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
        <NavigationHeader
          title={categoryName || 'Kategori Detayı'}
          onBackPress={onGoBack}
          backgroundColor={colors.background.primary}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Kategori bilgileri yükleniyor...
          </Text>
        </View>
      </View>
    );
  }

  if (!category) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
        <NavigationHeader
          title="Kategori Bulunamadı"
          onBackPress={onGoBack}
          backgroundColor={colors.background.primary}
        />
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>❌</Text>
          <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
            Kategori bulunamadı
          </Text>
          <Text style={[styles.emptyStateText, { color: colors.text.secondary }]}>
            Bu kategori mevcut değil veya silinmiş olabilir.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
      <NavigationHeader
        title={category.name}
        onBackPress={onGoBack}
        backgroundColor={colors.background.primary}
        rightComponent={
          <TouchableOpacity onPress={handleEditCategory} style={styles.editButton}>
            <Ionicons name="pencil" size={20} color={colors.primary[500]} />
          </TouchableOpacity>
        }
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Category Info */}
        <View style={[styles.categoryCard, { backgroundColor: colors.background.secondary }]}>
          <View style={styles.categoryHeader}>
            <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
              <Text style={styles.categoryIconText}>{category.icon}</Text>
            </View>
            <View style={styles.categoryInfo}>
              <Text style={[styles.categoryName, { color: colors.text.primary }]}>
                {category.name}
              </Text>
              <Text style={[styles.categoryType, { color: colors.text.secondary }]}>
                {category.type === 'income' ? 'Gelir Kategorisi' : 'Gider Kategorisi'}
              </Text>
              {category.isDefault && (
                <View style={[styles.defaultBadge, { backgroundColor: colors.primary[100] }]}>
                  <Text style={[styles.defaultBadgeText, { color: colors.primary[700] }]}>
                    Varsayılan
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary[500] }]}
            onPress={handleEditCategory}
          >
            <Ionicons name="pencil" size={20} color={colors.background.primary} />
            <Text style={[styles.actionButtonText, { color: colors.background.primary }]}>
              Düzenle
            </Text>
          </TouchableOpacity>
          
          {!category.isDefault && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.error[500] }]}
              onPress={handleDeleteCategory}
            >
              <Ionicons name="trash" size={20} color={colors.background.primary} />
              <Text style={[styles.actionButtonText, { color: colors.background.primary }]}>
                Sil
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Transactions */}
        <View style={styles.transactionsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            İşlemler ({transactions.length})
          </Text>
          
          {transactions.length > 0 ? (
            transactions.map(renderTransactionItem)
          ) : (
            renderEmptyTransactions()
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  
  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    ...typography.styles.body,
  },

  // Category Card
  categoryCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryIconText: {
    fontSize: 24,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...typography.styles.h3,
    marginBottom: 4,
  },
  categoryType: {
    ...typography.styles.body,
    marginBottom: 8,
  },
  defaultBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultBadgeText: {
    ...typography.styles.caption,
    fontWeight: '600',
  },

  // Actions
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    ...typography.styles.button,
  },

  // Edit Button
  editButton: {
    padding: 8,
  },

  // Transactions
  transactionsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    ...typography.styles.h4,
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  transactionLeft: {
    flex: 1,
  },
  transactionDescription: {
    ...typography.styles.body,
    fontWeight: '600',
    marginBottom: 4,
  },
  transactionDate: {
    ...typography.styles.caption,
  },
  transactionAmount: {
    ...typography.styles.h4,
    fontWeight: '700',
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    ...typography.styles.h4,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.body,
    textAlign: 'center',
    maxWidth: 280,
  },
});

export default CategoryDetailScreen;
