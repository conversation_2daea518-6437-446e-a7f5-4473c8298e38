import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Linking,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface FAQItemProps {
  question: string;
  answer: string;
  isExpanded: boolean;
  onToggle: () => void;
  styles: any;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, isExpanded, onToggle, styles }) => (
  <View style={styles.faqItem}>
    <TouchableOpacity style={styles.faqQuestion} onPress={onToggle}>
      <Text style={styles.faqQuestionText}>{question}</Text>
      <Text style={styles.faqToggle}>{isExpanded ? '−' : '+'}</Text>
    </TouchableOpacity>
    {isExpanded && (
      <View style={styles.faqAnswer}>
        <Text style={styles.faqAnswerText}>{answer}</Text>
      </View>
    )}
  </View>
);

interface ContactItemProps {
  icon: string;
  title: string;
  subtitle: string;
  onPress: () => void;
  styles: any;
}

const ContactItem: React.FC<ContactItemProps> = ({ icon, title, subtitle, onPress, styles }) => (
  <TouchableOpacity style={styles.contactItem} onPress={onPress}>
    <View style={styles.contactIconContainer}>
      <Ionicons name={icon} size={20} color="#6b7280" />
    </View>
    <View style={styles.contactTextContainer}>
      <Text style={styles.contactTitle}>{title}</Text>
      <Text style={styles.contactSubtitle}>{subtitle}</Text>
    </View>
    <Ionicons name="chevron-forward" size={16} color="#9ca3af" />
  </TouchableOpacity>
);

const HelpScreen: React.FC = () => {
  const colors = useThemedColors();
  const themedColors = useThemedColors();
  const styles = createStyles(themedColors);
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const faqData = [
    {
      question: 'Nasıl hesap oluşturabilirim?',
      answer: 'Ana sayfada "Kayıt Ol" butonuna tıklayarak e-posta adresiniz ve şifrenizle hesap oluşturabilirsiniz. Alternatif olarak Google veya Apple hesabınızla da giriş yapabilirsiniz.'
    },
    {
      question: 'Verilerim güvende mi?',
      answer: 'Evet, tüm verileriniz şifrelenmiş olarak saklanır ve sadece sizin erişiminizde bulunur. Kişisel bilgilerinizi üçüncü taraflarla paylaşmayız.'
    },
    {
      question: 'Nasıl kategori ekleyebilirim?',
      answer: 'Profil sayfasından "Kategoriler" bölümüne giderek yeni kategori ekleyebilirsiniz. İstediğiniz ikonu ve rengi seçebilirsiniz.'
    },
    {
      question: 'Verilerimi nasıl yedekleyebilirim?',
      answer: 'Ayarlar sayfasından "Otomatik Yedekleme" özelliğini aktif edebilir veya "Veri Dışa Aktar" ile manuel olarak yedekleyebilirsiniz.'
    },
    {
      question: 'Şifremi unuttum, ne yapmalıyım?',
      answer: 'Giriş sayfasında "Şifremi Unuttum" linkine tıklayarak e-posta adresinize şifre sıfırlama linki gönderebilirsiniz.'
    },
    {
      question: 'Uygulamayı nasıl güncelleyebilirim?',
      answer: 'App Store veya Google Play Store\'dan uygulamanın son sürümünü indirebilirsiniz. Otomatik güncellemeleri aktif etmenizi öneririz.'
    },
    {
      question: 'Hesabımı nasıl silebilirim?',
      answer: 'Ayarlar sayfasından "Hesabı Sil" seçeneğini kullanabilirsiniz. Bu işlem geri alınamaz ve tüm verileriniz silinir.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  const handleEmailSupport = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Destek Talebi');
  };

  const handleWhatsAppSupport = () => {
    Linking.openURL('https://wa.me/905417173986?text=Merhaba, Bütçe360 hakkında yardıma ihtiyacım var.');
  };

  const handleWebsite = () => {
    Linking.openURL('https://www.butce360.com');
  };

  const handlePrivacyPolicy = () => {
    Linking.openURL('https://www.butce360.com/privacy');
  };

  const handleTermsOfService = () => {
    Linking.openURL('https://www.butce360.com/terms');
  };

  const handleRateApp = () => {
    Alert.alert(
      'Uygulamayı Değerlendir',
      'Bütçe360\'ı App Store\'da değerlendirmek ister misiniz?',
      [
        { text: 'Şimdi Değil', style: 'cancel' },
        { text: 'Değerlendir', onPress: () => {
          // TODO: Open App Store rating
          Linking.openURL('https://apps.apple.com/app/butce360');
        }}
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hızlı İşlemler</Text>
          <View style={styles.sectionContent}>
            <ContactItem
              icon="star-outline"
              title="Uygulamayı Değerlendir"
              subtitle="App Store'da puan verin"
              onPress={handleRateApp}
              styles={styles}
            />
            <ContactItem
              icon="globe-outline"
              title="Web Sitesi"
              subtitle="www.butce360.com"
              onPress={handleWebsite}
              styles={styles}
            />
          </View>
        </View>

        {/* Contact Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>İletişim</Text>
          <View style={styles.sectionContent}>
            <ContactItem
              icon="mail-outline"
              title="E-posta Desteği"
              subtitle="<EMAIL>"
              onPress={handleEmailSupport}
              styles={styles}
            />
            <ContactItem
              icon="chatbubble-outline"
              title="WhatsApp Desteği"
              subtitle="Hızlı yanıt için WhatsApp"
              onPress={handleWhatsAppSupport}
              styles={styles}
            />
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sık Sorulan Sorular</Text>
          <View style={styles.sectionContent}>
            {faqData.map((faq, index) => (
              <FAQItem
                key={index}
                question={faq.question}
                answer={faq.answer}
                isExpanded={expandedFAQ === index}
                onToggle={() => toggleFAQ(index)}
                styles={styles}
              />
            ))}
          </View>
        </View>

        {/* Legal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Yasal</Text>
          <View style={styles.sectionContent}>
            <ContactItem
              icon="shield-checkmark-outline"
              title="Gizlilik Politikası"
              subtitle="Verilerinizi nasıl koruduğumuz"
              onPress={handlePrivacyPolicy}
              styles={styles}
            />
            <ContactItem
              icon="document-text-outline"
              title="Kullanım Şartları"
              subtitle="Hizmet şartları ve koşulları"
              onPress={handleTermsOfService}
              styles={styles}
            />
          </View>
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoTitle}>Bütçe360</Text>
          <Text style={styles.appInfoText}>Sürüm 1.0.0</Text>
          <Text style={styles.appInfoText}>© 2024 Nocy Tech</Text>
          <Text style={styles.appInfoText}>Tüm hakları saklıdır</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.primary[500],
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionContent: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    borderRadius: spacing.md,
    overflow: 'hidden',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  contactIconContainer: {
    width: 24,
    height: 24,
    marginRight: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactTextContainer: {
    flex: 1,
  },
  contactTitle: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '500',
  },
  contactSubtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  contactArrow: {
    fontSize: 20,
    color: colors.text.tertiary,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  faqQuestion: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  faqQuestionText: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '500',
    flex: 1,
    marginRight: spacing.md,
  },
  faqToggle: {
    fontSize: 20,
    color: colors.primary[500],
    fontWeight: 'bold',
  },
  faqAnswer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: colors.background.secondary,
  },
  faqAnswerText: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    lineHeight: 22,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  appInfoTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  appInfoText: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },
});

export default HelpScreen;
