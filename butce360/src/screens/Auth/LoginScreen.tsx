import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { useAuth } from '../../context/AuthContext';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from '../../components/common/Logo';
import { GoogleSignInService } from '../../services/googleSignInService';
import { authService } from '../../services/authService';
import { appleAuth } from '@invertase/react-native-apple-authentication';

interface LoginScreenProps {
  onNavigate?: (screen: string) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const { login, googleSignIn, appleSignIn, checkAuthStatus, enableGuestMode } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Hata', 'Lütfen tüm alanları doldurun.');
      return;
    }

    setLoading(true);
    try {
      await login({ username: formData.email, password: formData.password });
      // Sadece giriş başarılıysa ana sayfaya yönlendir
      onNavigate?.('Home');
    } catch (error) {
      console.error('[LoginScreen] Login error:', error);
      Alert.alert(
        'Hata',
        'Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin.',
      );
      // Hata durumunda sayfada kal, yönlendirme yapma
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setLoading(true);

      const googleSignInService = GoogleSignInService.getInstance();
      googleSignInService.configure();

      const result = await googleSignInService.signIn();
      if (!result.success) {
        Alert.alert('Hata', result.error || 'Google ile giriş yapılamadı.');
        return;
      }

      console.log('Google Sign-In başarılı:', result.user);

      // AuthContext'e user ve token'ı set et
      await googleSignIn(result.user, result.token!);

      await checkAuthStatus();
      // Sadece başarılıysa navigate et
      onNavigate?.('Home');
    } catch (error) {
      console.error('Google Sign-In Error:', error);
      Alert.alert('Hata', 'Google ile giriş yapılamadı.');
      // Hata durumunda sayfada kal
    } finally {
      setLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
  try {
    setLoading(true);

    // Gerçek cihazda Apple Sign In
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
    });

    const { identityToken, user, email, fullName } = appleAuthRequestResponse;

    if (!identityToken || !user) {
      throw new Error('Apple Identity token alınamadı.');
    }

    // Backend’e gönderilecek payload
    const payload = {
      identityToken,
      userIdentifier: user,
      email: email || undefined,
      fullName: fullName
        ? {
            givenName: fullName.givenName || undefined,
            familyName: fullName.familyName || undefined,
          }
        : undefined,
    };

    // Backend doğrulaması
    const response = await authService.appleSignIn(payload);

    // AuthContext güncelle
    await appleSignIn(response.user, response.token);

    // Sadece başarılıysa navigate et
    onNavigate?.('Home');
  } catch (error: any) {
    console.error('[LoginScreen] Apple Sign-In Error:', error);
    Alert.alert('Hata', error.message || 'Apple ile giriş yapılamadı.');
    // Hata durumunda sayfada kal
  } finally {
    setLoading(false);
  }
};


  const handleGuestLogin = async () => {
    try {
      setLoading(true);
      await enableGuestMode();
      onNavigate?.('Home');
    } catch (error) {
      console.error('[LoginScreen] Guest login error:', error);
      Alert.alert('Hata', 'Misafir girişi yapılamadı.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />

      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <Logo size={80} />
            <Text style={styles.title}>Hoş Geldiniz</Text>
            <Text style={styles.subtitle}>Hesabınıza giriş yapın</Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>E-posta</Text>
              <View style={styles.inputWrapper}>
                <Ionicons
                  name="mail-outline"
                  size={20}
                  color={colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="E-posta adresinizi girin"
                  placeholderTextColor={colors.text.tertiary}
                  value={formData.email}
                  onChangeText={text =>
                    setFormData({ ...formData, email: text })
                  }
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Şifre</Text>
              <View style={styles.inputWrapper}>
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Şifrenizi girin"
                  placeholderTextColor={colors.text.tertiary}
                  value={formData.password}
                  onChangeText={text =>
                    setFormData({ ...formData, password: text })
                  }
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? 'eye-outline' : 'eye-off-outline'}
                    size={20}
                    color={colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={styles.forgotPassword}
              onPress={() =>
                Alert.alert(
                  'Bilgi',
                  'Şifre sıfırlama özelliği yakında eklenecek.',
                )
              }
            >
              <Text style={styles.forgotPasswordText}>Şifremi Unuttum</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.loginButton,
                { backgroundColor: colors.primary[500] },
              ]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text
                style={[
                  styles.loginButtonText,
                  { color: colors.background.secondary },
                ]}
              >
                {loading ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
              </Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View
                style={[
                  styles.dividerLine,
                  { backgroundColor: colors.border.primary },
                ]}
              />
              <Text style={styles.dividerText}>veya</Text>
              <View
                style={[
                  styles.dividerLine,
                  { backgroundColor: colors.border.primary },
                ]}
              />
            </View>

            {/* Social Login Buttons */}
            <View style={styles.socialButtons}>
              {/* Apple Sign-In */}
              <TouchableOpacity
                style={[styles.socialButton, styles.appleButton]}
                onPress={handleAppleSignIn}
                disabled={loading}
              >
                <Ionicons
                  name="logo-apple"
                  size={20}
                  color="#FFFFFF"
                  style={styles.socialIcon}
                />
                <Text style={[styles.socialButtonText, styles.appleButtonText]}>
                  Apple ile Giriş Yap
                </Text>
              </TouchableOpacity>

              {/* Google Sign-In */}
              <TouchableOpacity
                style={[styles.socialButton, styles.googleButton]}
                onPress={handleGoogleSignIn}
                disabled={loading}
              >
                <Ionicons
                  name="logo-google"
                  size={20}
                  color="#DB4437"
                  style={styles.socialIcon}
                />
                <Text
                  style={[styles.socialButtonText, styles.googleButtonText]}
                >
                  Google ile Giriş Yap
                </Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[
                styles.guestButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={handleGuestLogin}
            >
              <Ionicons
                name="person-outline"
                size={20}
                color={colors.text.primary}
                style={styles.guestIcon}
              />
              <Text
                style={[styles.guestButtonText, { color: colors.text.primary }]}
              >
                Misafir Olarak Devam Et
              </Text>
            </TouchableOpacity>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>Hesabınız yok mu?</Text>
            <TouchableOpacity onPress={() => onNavigate?.('register')}>
              <Text style={[styles.footerLink, { color: colors.primary[500] }]}>
                Kayıt Ol
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: spacing.screenPadding,
    },
    header: {
      alignItems: 'center',
      paddingTop: spacing['4xl'],
      paddingBottom: spacing['2xl'],
    },
    title: {
      ...typography.styles.h2,
      color: colors.text.primary,
      marginTop: spacing.lg,
      marginBottom: spacing.sm,
    },
    subtitle: {
      ...typography.styles.body1,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    form: {
      flex: 1,
      paddingVertical: spacing.xl,
    },
    inputContainer: {
      marginBottom: spacing.lg,
    },
    inputLabel: {
      ...typography.styles.body2,
      color: colors.text.primary,
      marginBottom: spacing.sm,
      fontWeight: '600',
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      borderRadius: spacing.cardRadius,
      borderWidth: 1,
      borderColor: colors.border.primary,
    },
    inputIcon: {
      marginLeft: spacing.md,
    },
    input: {
      flex: 1,
      ...typography.styles.body1,
      color: colors.text.primary,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.sm,
    },
    passwordToggle: {
      padding: spacing.md,
    },
    forgotPassword: {
      alignSelf: 'flex-end',
      marginBottom: spacing.xl,
    },
    forgotPasswordText: {
      ...typography.styles.body2,
      color: colors.primary[500],
      fontWeight: '600',
    },
    loginButton: {
      paddingVertical: spacing.buttonPaddingVertical,
      borderRadius: spacing.cardRadius,
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    loginButtonText: {
      ...typography.styles.button,
      fontWeight: '600',
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: spacing.lg,
    },
    dividerLine: {
      flex: 1,
      height: 1,
    },
    dividerText: {
      ...typography.styles.caption,
      color: colors.text.tertiary,
      marginHorizontal: spacing.md,
    },
    guestButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.buttonPaddingVertical,
      borderRadius: spacing.cardRadius,
      borderWidth: 1,
      borderColor: colors.border.primary,
    },
    guestIcon: {
      marginRight: spacing.sm,
    },
    guestButtonText: {
      ...typography.styles.button,
      fontWeight: '500',
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: spacing.xl,
    },
    footerText: {
      ...typography.styles.body2,
      color: colors.text.secondary,
      marginRight: spacing.sm,
    },
    footerLink: {
      ...typography.styles.body2,
      fontWeight: '600',
    },

    // Social Login Styles
    socialButtons: {
      gap: spacing.md,
      marginBottom: spacing.lg,
    },
    socialButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.md,
      borderRadius: spacing.sm,
      borderWidth: 1,
    },
    appleButton: {
      backgroundColor: '#000000',
      borderColor: '#000000',
    },
    googleButton: {
      backgroundColor: '#FFFFFF',
      borderColor: '#DADCE0',
    },
    socialIcon: {
      marginRight: spacing.sm,
    },
    socialButtonText: {
      ...typography.styles.button,
      fontWeight: '600',
    },
    appleButtonText: {
      color: '#FFFFFF',
    },
    googleButtonText: {
      color: '#3C4043',
    },
  });

export default LoginScreen;
