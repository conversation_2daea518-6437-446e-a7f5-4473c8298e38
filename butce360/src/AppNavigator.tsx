import React from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { useNavigation } from './navigation/AppNavigator';
import { useAuth } from './hooks/useAuth';
import { useThemedColors, useThemeState } from './hooks/useThemedStyles';

// Import screens
import WelcomePage from './screens/Welcome/WelcomePage';
import LoginScreen from './screens/Auth/LoginScreen';
import RegisterScreen from './screens/Auth/RegisterScreen';
import HomeScreen from './screens/Home/HomeScreen';
import AddTransactionScreen from './screens/Transactions/AddTransactionScreen';
// import TransactionListScreen from './screens/Transactions/TransactionListScreen';
import TransactionsScreen from './screens/Transactions/TransactionsScreen';
import BudgetScreen from './screens/Budget/BudgetScreen';
import AddBudgetScreen from './screens/Budget/AddBudgetScreen';
import CategoriesScreen from './screens/Categories/CategoriesScreen';
import AccountsScreen from './screens/Accounts/AccountsScreen';
import AddAccountScreen from './screens/Accounts/AddAccountScreen';
import ReportsScreen from './screens/Reports/ReportsScreen';
import CategoryReportScreen from './screens/Reports/CategoryReportScreen';
import MonthlyReportScreen from './screens/Reports/MonthlyReportScreen';
import ProfileScreen from './screens/Profile/ProfileScreen';
import EditProfileScreen from './screens/Profile/EditProfileScreen';
import ProfileDetailsScreen from './screens/Profile/ProfileDetailsScreen';
import ChangePasswordScreen from './screens/Profile/ChangePasswordScreen';
import AddCategoryScreen from './screens/Categories/AddCategoryScreen';
import SettingsScreen from './screens/Settings/SettingsScreen';
import HelpScreen from './screens/Help/HelpScreen';
import AboutScreen from './screens/About/AboutScreen';

// Import components
import TabBar from './components/common/TabBar';
// import LoadingSpinner from './components/common/LoadingSpinner';
import SplashScreen from './components/common/SplashScreen';

// Main App Navigator
const AppNavigator: React.FC = () => {
  const { navigationState, navigate } = useNavigation();
  const { state: authState } = useAuth();
  const colors = useThemedColors();
  const { isDark } = useThemeState();

  const renderScreen = () => {
    const { currentScreen, params } = navigationState;

    // Show splash screen while checking auth
    if (authState.isLoading) {
      return <SplashScreen />;
    }

    // Route to appropriate screen based on current screen
    switch (currentScreen) {
      case 'Welcome':
        return <WelcomePage />;

      case 'Login':
        return <LoginScreen />;

      case 'Register':
        return <RegisterScreen />;

      case 'MainTabs':
      case 'Home':
      case 'Transactions':
      case 'Budget':
      case 'Reports':
      case 'Profile':
        return renderMainApp();

      case 'AddTransaction':
      case 'EditTransaction':
        return <AddTransactionScreen onNavigate={navigate} />;

      case 'AddBudget':
      case 'EditBudget':
        return <AddBudgetScreen route={{ params: params }} />;

      case 'Categories':
        return <CategoriesScreen />;

      case 'Accounts':
        return <AccountsScreen />;

      case 'About':
        return <AboutScreen />;

      case 'EditProfile':
        return <EditProfileScreen />;

      case 'ProfileDetails':
        return <ProfileDetailsScreen onNavigate={navigate} />;

      case 'Settings':
        return <SettingsScreen />;

      case 'Help':
        return <HelpScreen />;

      case 'ChangePassword':
        return <ChangePasswordScreen />;

      case 'AddCategory':
        return <AddCategoryScreen />;

      case 'AddAccount':
        return <AddAccountScreen />;

      case 'CategoryReport':
        return <CategoryReportScreen />;

      case 'MonthlyReport':
        return <MonthlyReportScreen />;

      // Add more screens as needed
      default:
        // Default to Welcome screen
        return <WelcomePage />;
    }
  };

  const renderMainApp = () => {
    const { currentScreen } = navigationState;

    // Check if user is authenticated (not guest) for protected screens
    if (!authState.isAuthenticated && !authState.isGuest) {
      // User is not authenticated, redirect to Welcome
      return <WelcomePage />;
    }

    // Determine current tab
    let currentTab = 'home';
    switch (currentScreen) {
      case 'Transactions':
        currentTab = 'transactions';
        break;
      case 'Budget':
        currentTab = 'budget';
        break;
      case 'Reports':
        currentTab = 'reports';
        break;
      case 'Profile':
        currentTab = 'profile';
        break;
      default:
        currentTab = 'home';
    }

    return (
      <View style={styles.mainApp}>
        <View style={styles.screenContainer}>
          {renderMainScreen()}
        </View>
        <TabBar activeTab={currentTab} />
      </View>
    );
  };

  const renderMainScreen = () => {
    const { currentScreen } = navigationState;

    switch (currentScreen) {
      case 'Transactions':
        return <TransactionsScreen />;
      case 'Budget':
        return <BudgetScreen />;
      case 'Reports':
        return <ReportsScreen />;
      case 'Profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen />;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.primary,
    },
    mainApp: {
      flex: 1,
    },
    screenContainer: {
      flex: 1,
    },
  });

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor={colors.background.primary}
      />
      {renderScreen()}
    </View>
  );
};



export default AppNavigator;
